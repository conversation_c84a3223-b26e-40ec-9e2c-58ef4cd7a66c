#!/bin/bash
DIR="$(cd "$(dirname "$0")" && pwd)"
environment=$1
git_commit_id=$2

PROPERTIES_FILE=$DIR/../gradle.properties
# Variable to hold the Property Value
prop_value=""

function getProperty() {
  prop_key=$1
  prop_value=$(cat ${PROPERTIES_FILE} | grep ${prop_key} | cut -d'=' -f2)
}

function version_generator() {

  if [ -f "$PROPERTIES_FILE" ]; then
    #echo "$PROPERTIES_FILE exists."
    getProperty "version"
    #echo "Key = version ; Value = " ${prop_value}
    export base_version=${prop_value}

    if [ -z "$base_version" ]; then
      #Setting to 1.0.0 if version variable doesnt exist
      base_version="1.0.0"
      #echo "Base Version Value: $base_version"
    fi

  else
    #Setting to 1.0.0 if file doesnt exist
    base_version="1.0.0"
  fi

  if [ $environment = 'qa' ]; then
    image_tag="$base_version-RC-${git_commit_id:0:7}"
  elif [ $environment = 'uat' ]; then
    image_tag="$base_version-UAT-${git_commit_id:0:7}"
  elif [ $environment = 'prod' ]; then
    image_tag="$base_version"
  else
    image_tag="$base_version-SNAPSHOT-${git_commit_id:0:7}"
  fi

}

#Invoke version generator
version_generator
echo $image_tag
