variables:
  ECR_REPOSITORY: pam-psi

include:
- project: 'gitlab-common-components/cicd-templates'
  ref: main
  file: 'v2/cicd-gitlab-base.yml'

stages:
  - code_scan
  - test
  - Qa
  - Prod

# Create a Gitlab Release
.create-release:
  script: echo 'Creating release for v$RELEASE_TAG'
  release:
    tag_name: 'v$RELEASE_TAG'
    description: 'v$RELEASE_TAG'
    ref: $CI_COMMIT_SHA
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# be_tests:
#   stage: test
#   script:
#     - ./gradlew unitTest
#   tags:
#     - pull_request
#   rules:
#     - if: $CI_PIPELINE_SOURCE == 'merge_request_event' || $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

Tag Qa:
  extends: [.tag-image]
  stage: Qa
  script: echo 'No image to tag'
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  variables:
    APP_ENV: "qa"

Build and Deploy Qa:
  stage: Qa
  variables:
    BUILD_COMMAND: "./cicd/build-image.sh $RELEASE_TAG $ECR_REPOSITORY $NonProdAccountNumber"
    DEPLOY_COMMAND: "./deploy/deploy-qa.sh $RELEASE_TAG"
    APP_ENV: "qa"
  trigger:
    include:
    - project: 'gitlab-common-components/cicd-templates'
      ref: main
      file: 'v2/cicd-gitlab-ci.yml'
    strategy: depend
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  needs:
    - job: Tag Qa
      artifacts: true

Tag Prod:
  extends: [.tag-image]
  stage: Prod
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
  variables:
    APP_ENV: "prod"
    PREV_ENV: "qa"

Release Prod:
  extends: [.create-release]
  stage: Prod
  tags: [prod]
  needs:
    - job: Tag Prod
      artifacts: true

Deploy Prod:
  stage: Prod
  variables:
    RELEASE_TAG: $RELEASE_TAG
    DEPLOY_COMMAND: "./deploy/deploy-prod.sh $RELEASE_TAG"
    APP_ENV: "prod"
  trigger:
    include:
      - project: 'gitlab-common-components/cicd-templates'
        ref: main
        file: 'v2/cicd-gitlab-ci-prod.yml'
    strategy: depend
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  needs:
    - job: Tag Prod
      artifacts: true
