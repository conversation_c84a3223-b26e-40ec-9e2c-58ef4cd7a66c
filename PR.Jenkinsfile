script {
    library identifier: "pam-psi-pr@$env.GIT_COMMIT", retriever: legacySCM(scm)
}
pipeline{
  agent none
  stages{
    stage("PR Check"){
      when {
        anyOf {
          expression {
            return pipelineUtils.hasChangesIn('src')
          }
          expression {
            return pipelineUtils.hasChangesIn('gradle')
          }
          expression {
            return pipelineUtils.hasChangesIn('vars')
          }
          expression {
            return pipelineUtils.hasChangesIn('build.gradle')
          }
          expression {
            return pipelineUtils.hasChangesIn('settings.gradle')
          }
          expression {
            return pipelineUtils.hasChangesIn('PR.Jenkinsfile')
          }
        }
      }
      agent {
        node {
          label 'JDK11'
        }
      }
      steps{
        script {
          applicationPipeline.prCheck()
        }
      }
    }
  }
}
