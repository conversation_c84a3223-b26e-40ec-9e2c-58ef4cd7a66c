# PSI (PAM Salesforce Integration) - Comprehensive System Summary

## Overview

PSI is a **PAM Salesforce Integration** system developed by NBCUniversal that serves as a critical data synchronization bridge between the PAM (Pricing and Media) system and Salesforce CRM. The system is built using Spring Boot and Java 11, designed to run as a scheduled batch job that processes advertising deal data and synchronizes it with Salesforce.

## Core Purpose

The primary function of PSI is to:
1. **Extract** advertising deal data from the PAM database
2. **Transform** the data into Salesforce-compatible format
3. **Load** the data into Salesforce as Deal and Opportunity records
4. **Synchronize** bidirectional updates between PAM and Salesforce
5. **Handle** special events and quarterly deal processing

## System Architecture

### Technology Stack
- **Framework**: Spring Boot 2.5
- **Language**: Java 11
- **Database**: Oracle Database (via JDBC)
- **Cloud Services**: AWS (S3, SES, Secrets Manager)
- **Build Tool**: Gradle
- **Containerization**: Docker (via Jib plugin)
- **Deployment**: AWS ECS with CloudFormation

### Key Components

#### 1. Main Application (`PsiApplication.java`)
- **Entry Point**: CommandLineRunner implementation
- **Core Functions**:
  - `syncOpportunityStage()`: Syncs opportunity stage changes from Salesforce back to PAM
  - `processParentDeals()`: Processes new/updated parent deals from PAM to Salesforce
  - `processSyncParentDeals()`: Syncs existing deals with updated data
  - `syncSpecialEvents()`: Handles special event deal synchronization

#### 2. Global Context (`Global.java`)
- **Central State Management**: Maintains current processing context
- **Domain Model Cache**: Holds references to core business entities
- **Configuration**: Manages budget years, quarters, and naming conventions
- **Utility Functions**: Provides ID formatting and name generation

#### 3. Data Layer
- **Repositories**: JPA repositories for all domain entities
- **Services**: Business logic layer with service implementations
- **Models**: Rich domain models representing PAM business entities

#### 4. Salesforce Integration (`sf` package)
- **SfClient**: Low-level Salesforce API client
- **SfLogic**: High-level business logic for Salesforce operations
- **SfJsonBuilder**: Constructs Salesforce composite API payloads
- **Exception Handling**: Custom exceptions for API failures

#### 5. PAM API Integration (`pam` package)
- **PamApiClient**: RESTful client for PAM API callbacks
- **Callback Operations**: Updates PAM with Salesforce IDs and sync status

## Core Data Model

### Primary Entities

#### Parent Deal Hierarchy
```
ParentDeal (Container for related deals)
├── Deal (Individual advertising deals)
│   └── Budget (Financial allocations per quarter)
└── DealLink (Grouped deals with shared characteristics)
    └── Multiple Deals
```

#### Key Domain Objects
- **Deal**: Core advertising deal entity with advertiser, agency, property relationships
- **Budget**: Financial data with quarterly breakdowns and Salesforce sync flags
- **ParentDeal**: Logical grouping of related deals for a client/campaign
- **DealLink**: Mechanism to group multiple deals with shared properties
- **Property**: Media properties (TV networks, digital platforms)
- **Agency/Advertiser**: Client relationship entities
- **SpecialEvent**: Special advertising events (Olympics, Super Bowl, etc.)

### Data Flow

#### 1. PAM → Salesforce (Primary Flow)
```
PAM Database → DataService → ParentDealRecord → SfLogic → Salesforce
```

#### 2. Salesforce → PAM (Callback Flow)
```
Salesforce → OpportunityHistory → PSI → PamApiClient → PAM Database
```

## Processing Logic

### 1. Parent Deal Processing
- **Query**: Fetches parent deals modified since last run
- **Transform**: Converts PAM data to Salesforce format
- **Upsert**: Creates/updates Salesforce Deal records
- **Callback**: Updates PAM with Salesforce IDs

### 2. Deal Processing
- **Single Deals**: Creates one Opportunity per Deal
- **Quarterly Deals**: Creates multiple Opportunities (one per quarter)
- **Deal Links**: Creates one Opportunity representing multiple deals

### 3. Special Event Processing
- **Event-Driven**: Processes special advertising events
- **Complex Mapping**: Handles unique business rules for special events
- **Quarterly Handling**: Manages quarter-specific event details

### 4. Opportunity Stage Sync
- **Reverse Sync**: Monitors Salesforce opportunity stage changes
- **Status Update**: Updates PAM deal sync status based on Salesforce changes
- **Configurable Stages**: Processes specific stage names from configuration

## Configuration Management

### Environment Profiles
- **Local**: Development environment with local database
- **QA**: Testing environment with QA PAM API
- **Production**: Live environment with production systems

### Key Configuration
- **Database**: Oracle connection for PAM data
- **PAM API**: RESTful endpoints for callbacks
- **Salesforce**: OAuth and composite API configuration
- **S3**: State persistence for last processed timestamps
- **Email**: Error notification recipients

## Error Handling & Monitoring

### Exception Management
- **SfApiException**: Salesforce API-specific errors
- **SfOtherException**: General Salesforce errors
- **Systematic Errors**: Database, network, configuration issues

### Notification System
- **Email Alerts**: Engineering team notifications for failures
- **Detailed Logging**: Per-parent-deal log folders with summaries
- **S3 Logging**: Persistent storage of processing logs

### Resilience Features
- **Incremental Processing**: Only processes changes since last run
- **Individual Failure Isolation**: One parent deal failure doesn't stop others
- **Retry Logic**: Built into Salesforce composite API calls

## Deployment & Operations

### Containerization
- **Docker**: Jib plugin creates optimized container images
- **ECR**: AWS Elastic Container Registry for image storage
- **Base Image**: NBCUniversal standard JDK 11 image

### Scheduling
- **ECS Scheduled Tasks**: Runs every 5 minutes via CloudFormation
- **State Management**: S3-based timestamp tracking prevents duplicate processing
- **Resource Allocation**: 256 CPU units, 512MB memory

### CI/CD Pipeline
- **Jenkins**: Automated build and deployment pipeline
- **Environment Promotion**: Separate pipelines for QA and Production
- **Version Tagging**: Automated versioning and image tagging

## Business Impact

### Critical Functions
- **Revenue Tracking**: Ensures advertising deal revenue is properly recorded in Salesforce
- **Sales Process**: Enables sales team to track deal progress and stages
- **Financial Reporting**: Provides accurate quarterly financial data
- **Client Management**: Maintains client relationship data consistency

### Data Volume
- **Processing Scale**: Handles hundreds of parent deals per run
- **Frequency**: Runs every 5 minutes for near real-time sync
- **Data Complexity**: Manages complex hierarchical deal structures

## Security & Compliance

### Authentication
- **AWS Secrets Manager**: Secure credential storage
- **OAuth**: Salesforce authentication
- **Service Accounts**: PAM API authentication

### Data Protection
- **Encryption**: In-transit and at-rest encryption
- **Access Control**: Role-based access to systems
- **Audit Trail**: Comprehensive logging of all operations

## Future Considerations

### Scalability
- **Horizontal Scaling**: Container-based architecture supports scaling
- **Database Optimization**: Query optimization for large datasets
- **API Rate Limiting**: Salesforce API usage monitoring

### Maintenance
- **Dependency Updates**: Regular Spring Boot and library updates
- **Database Schema**: Coordination with PAM schema changes
- **Salesforce Changes**: Adaptation to Salesforce API updates

This PSI system represents a critical integration point in NBCUniversal's advertising technology stack, ensuring seamless data flow between internal PAM systems and external Salesforce CRM for effective sales and revenue management.

## Detailed Technical Implementation

### Database Integration

#### Oracle Database Schema
- **Primary Schema**: `devpam` (local), `pam` (production)
- **Key Tables**:
  - `DEAL`: Core advertising deals with relationships to agencies, advertisers, properties
  - `BUDGET`: Financial allocations with quarterly amounts and Salesforce sync flags
  - `PARENT_DEAL`: Logical grouping of related deals
  - `DEAL_LINK`: Mechanism for grouping multiple deals
  - `SPECIAL_EVENT`: Special advertising events with complex business rules
  - `OPPORTUNITY_HISTORY`: Salesforce opportunity stage change tracking

#### JPA Configuration
- **Hibernate**: ORM with eager/lazy loading strategies
- **Physical Naming**: Preserves database column names (e.g., QUARTER_YEAR)
- **Connection Pooling**: Oracle JDBC with connection management
- **Transaction Management**: Spring-managed transactions

### Salesforce Integration Details

#### Composite API Usage
- **Batch Operations**: Multiple records in single API call
- **Relationship Management**: Handles parent-child record relationships
- **Error Handling**: Individual record failure isolation
- **Rate Limiting**: Respects Salesforce API limits

#### Object Mapping
```
PAM Entity → Salesforce Object
ParentDeal → Deal__c (Custom Object)
Deal → Opportunity (Standard Object)
DealLink → Opportunity (Standard Object)
Budget → Opportunity Line Items
```

#### Field Mapping Examples
- **PAM Deal ID**: Maps to `PAM_Deal_Id__c` with year suffix (e.g., "12345-2025")
- **Parent Deal ID**: Maps to `PAM_Parent_Deal_Id__c` with year suffix
- **Quarterly Amounts**: Maps to custom amount fields per quarter
- **Stage Names**: Configurable mapping between PAM and Salesforce stages

### Special Event Processing

#### Complex Business Logic
- **Event Types**: Olympics, Super Bowl, Award Shows, etc.
- **Quarter Handling**: Special rules for event-specific quarters
- **Detail Processing**: Granular event detail synchronization
- **Status Tracking**: Multi-stage approval and sync status

#### Event Context Management
```java
SpecialEventContext {
    - Event metadata
    - Deal relationships
    - Quarter mappings
    - Salesforce field mappings
    - Business rule configurations
}
```

### Logging and Monitoring

#### Structured Logging
- **Per-Parent-Deal Folders**: Isolated logging per processing unit
- **Log Levels**: DEBUG, INFO, ERROR with contextual information
- **S3 Persistence**: Long-term log storage and retrieval
- **Summary Reports**: Automated success/failure summaries

#### Monitoring Metrics
- **Processing Time**: Per-parent-deal and total execution time
- **Success Rates**: Deal processing success/failure ratios
- **API Performance**: Salesforce and PAM API response times
- **Error Patterns**: Categorized error analysis

### Performance Optimization

#### Query Optimization
- **Incremental Processing**: Only processes records changed since last run
- **Indexed Queries**: Leverages database indexes for timestamp-based queries
- **Batch Size Management**: Configurable batch sizes for large datasets
- **Connection Pooling**: Efficient database connection management

#### Memory Management
- **Lazy Loading**: JPA lazy loading for large object graphs
- **Garbage Collection**: Optimized for batch processing patterns
- **Resource Cleanup**: Proper cleanup of database and HTTP connections

### Error Recovery and Resilience

#### Failure Isolation
- **Parent Deal Level**: Individual parent deal failures don't affect others
- **Composite API**: Individual record failures within batch operations
- **Service Degradation**: Graceful handling of external service failures

#### Recovery Mechanisms
- **Timestamp Tracking**: S3-based state management for recovery
- **Idempotent Operations**: Safe to re-run processing
- **Manual Recovery**: Tools for reprocessing specific time ranges

### Security Implementation

#### Credential Management
- **AWS Secrets Manager**: Database passwords, API keys
- **Environment Variables**: Non-sensitive configuration
- **Service Accounts**: Dedicated accounts for system integration

#### Data Security
- **Encryption in Transit**: HTTPS for all API communications
- **Database Security**: Encrypted connections to Oracle
- **Access Logging**: Comprehensive audit trail

### Configuration Management

#### Environment-Specific Settings
```yaml
# Local Development
spring.datasource.url: ****************************************
pam.api-url: http://localhost:3001

# QA Environment
pam.api-url: https://pamapiqa.nonprod.adsalescloud.inbcu.com

# Production
pam.api-url: https://pamapiprod.adsalescloud.inbcu.com
```

#### Business Rule Configuration
- **Stage Names**: Configurable Salesforce stage names to monitor
- **Deal Types**: Configurable deal type processing rules
- **Quarter Mappings**: Flexible quarter date calculations
- **Property Mappings**: Media property to Salesforce product mappings

This comprehensive technical implementation ensures robust, scalable, and maintainable integration between PAM and Salesforce systems.
