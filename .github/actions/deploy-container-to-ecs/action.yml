name: 'Deploy Container to ECS'

inputs:
  environment:
    description: 'Environment to deploy to'
    required: true
    default: 'dev'
  cluster:
    description: 'ECS Cluster to deploy to'
    required: true
  region:
    description: 'AWS region to deploy to'
    required: true
    default: 'us-east-1'
  repository_name:
    description: 'Name of the ECR repository to deploy to'
    required: true
  ecr_image:
    description: 'Name of the image to deploy'
    required: true
  task_definition:
    description: 'Name of the task definition to deploy'
    required: true
  container_command:
    description: 'Path containing command to assign to the container definition'
  role:
    description: 'AWS role to assume'
    required: true
    default: '904541710863:role/customapps-github-actions-nonprod-role'

runs:
  using: composite
  steps:
    - name: configure aws credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        role-to-assume: arn:aws:iam::${{ inputs.role }}
        role-session-name: samplerolesession # dynamically generate
        aws-region: ${{ inputs.region }}

    - name: Download task definition
      shell: bash
      run: |
        aws ecs describe-task-definition --task-definition ${{ inputs.task_definition }} --query taskDefinition > task-definition.json
        containerName=$(jq -r '.containerDefinitions[].name' task-definition.json)
        echo "container-name=$containerName" >> $GITHUB_ENV
        jq 'del(.enableFaultInjection)' task-definition.json > task-definition.tmp.json
        mv task-definition.tmp.json task-definition.json
        if [ -n "${{ inputs.container_command }}" ]; then
          jq '.containerDefinitions[] += { "command": ["${{ inputs.container_command }}"] }' task-definition.json > task-definition.tmp.json
          mv task-definition.tmp.json task-definition.json
        fi

    - name: Output Task Definition
      shell: bash
      run: |
        cat task-definition.json
    
    - name: Upload Environment File to S3
      shell: bash
      run: |
        envFile=./deploy/env/${{ inputs.environment }}.env
        s3Object=adsales-appdev-config/${{ inputs.repository_name }}/env/${{ inputs.environment }}.env
        aws s3 cp "$envFile" "s3://$s3Object"
  
    - name: Fill in the new image ID in the Amazon ECS task definition
      id: task-def
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: task-definition.json
        container-name: ${{ env.container-name }}
        image: ${{ inputs.ecr_image }}
    
    # - name: Deploy to Amazon ECS Scheduled Tasks
    #   uses: airfordable/ecs-deploy-task-definition-to-scheduled-task@v2.0.0
    #   with:
    #     cluster: ${{ inputs.cluster }}
    #     rule-prefix: my-rule-prefix (optional, defaults to '')
    #     task-definition: ${{ steps.task-def.outputs.task-definition }}

    - name: Deploy Amazon ECS task definition
      uses: aws-actions/amazon-ecs-deploy-task-definition@df9643053eda01f169e64a0e60233aacca83799a
      with:
        task-definition: ${{ steps.task-def.outputs.task-definition }}
        cluster: ${{ inputs.cluster }}
        wait-for-service-stability: true
