name: 'Tag a Build Image'

inputs:
  sha:
    description: 'The SHA used to tag the image'
    required: true
  environment:
    description: 'The environment to create the tag for'
    required: true
    options:
      - dev
      - qa
      - prod
  deployment-account:
    required: true
    default: ************
  previous-environment:
    description: 'The previous environment in the build process'
    required: false
    options:
      - dev
      - qa
  ecr-repository:
    description: 'The ECR Repository to store the tagged image'
    required: false
    default: pam-psi

runs:
  using: composite

  steps:
    - name: Tag Image
      shell: bash
      run: |
        RELEASE_TAG=$(./cicd/version-utils.sh ${{ inputs.environment }} ${{ inputs.sha }})
        echo "RELEASE_TAG=$RELEASE_TAG" >> $GITHUB_ENV
        if [ ! -z "${{ inputs.previous-environment }}" ]; then
          PREV_TAG=$(./cicd/version-utils.sh ${{ inputs.previous-environment }} ${{ inputs.sha}})
          ./cicd/tag-version.sh $PREV_TAG $RELEASE_TAG ${{ inputs.ecr-repository }}
        fi
    - name: Set image output
      shell: bash
      run: echo "ecr-image=${{ inputs.deployment-account }}.dkr.ecr.us-east-1.amazonaws.com/${{ inputs.ecr-repository }}:${{ env.RELEASE_TAG }}" >> $GITHUB_ENV
    - name: Set release tag
      shell: bash
      run: echo "release-tag=${{ env.RELEASE_TAG }}" >> $GITHUB_ENV
