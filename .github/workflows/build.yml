name: Build and Deploy
on:
  push:
    branches:
      - release-*
  workflow_dispatch:
    inputs:
      ecr-deployment-account:
        type: string
        description: Account ID of account to deploy images to
        required: false
        default: '************'
      deploy-env:
        type: string
        description: Environment to deploy to
        required: true
        default: qa

env:
  repository-name: psi
  deployment-account: ************
  deployment-role: customapps-github-actions-nonprod-role
  prod-deployment-account: ************
  prod-deployment-role: customapps-github-actions-prod-role
  deployment-region: us-east-1
  GH_TOKEN: ${{ secrets.GH_TOKEN }}
  GH_USER: ${{ secrets.GH_USER }}
  BUNDLE_GITHUB__COM: ${{ secrets.GH_TOKEN }}:x-oauth-basic

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3
        with:
          persist-credentials: false

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: arn:aws:iam::${{ env.deployment-account }}:role/${{ env.deployment-role }}
          role-session-name: samplerolesession
          aws-region: ${{ env.deployment-region }}

      - name: Log in to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Tag Image QA
        if: startsWith( github.ref_name, 'release' ) || inputs.deploy-env == 'qa'
        id: tag-image-qa
        uses: ./.github/actions/tag-image
        with:
          sha: ${{ github.sha }}
          environment: qa

      - name: Build Image and Push to ECR
        if: startsWith( github.ref_name, 'release' ) || inputs.deploy-env == 'qa'
        shell: bash
        run: ./cicd/build-image.sh ${{ env.release-tag }}

      - name: Deploy QA
        if: startsWith( github.ref_name, 'release' ) || inputs.deploy-env == 'qa'
        uses: ./.github/actions/deploy-container-to-ecs
        with:
          environment: 'qa'
          cluster: 'ecs-custom-apps-pam-qa'
          region: ${{ env.deployment-region }}
          repository_name: ${{ env.repository-name }}
          ecr_image: ${{ env.ecr-image }}
          task_definition: psiqa-task-family

  update-scheduled-task:
    needs: build
    uses: NBCUniversal/dna-github-actions/.github/workflows/aws-ecs-task-scheduler-deploy-workflow.yml@add_reusable_workflows
    with:
      AWS_ROLE_ARN: arn:aws:iam::************:role/customapps-github-actions-nonprod-role
      BUILD_COMMAND: echo "build is completed"
      environment: qa
      cloudwatch_event_rule: psiqa-task-scheduler
      container_name: psiqa
      task_definition: psiqa-task-family
      ecr_image: ************.dkr.ecr.us-east-1.amazonaws.com/pam-psi:latest

  build-prod:
    if: startsWith( github.ref_name, 'release' )
    environment: prod
    runs-on: ubuntu-latest
    needs: [build]
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3
        with:
          persist-credentials: false

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: arn:aws:iam::${{ env.deployment-account }}:role/${{ env.deployment-role }}
          role-session-name: samplerolesession
          aws-region: ${{ env.deployment-region}}

      - name: Tag Image Prod
        id: tag-image-prod
        uses: ./.github/actions/tag-image
        with:
          sha: ${{ github.sha }}
          environment: prod
          previous-environment: qa

      - name: Deploy Prod
        uses: ./.github/actions/deploy-container-to-ecs
        with:
          environment: 'prod'
          cluster: 'ecs-custom-apps-pam-prod'
          region: ${{ env.deployment-region }}
          repository_name: ${{ env.repository-name }}
          ecr_image: ${{ env.ecr-image }}
          task_definition: psiprod-task-family
          role: ${{ env.prod-deployment-account}}:role/${{ env.prod-deployment-role }}

  update-scheduled-task-prod:
    needs: build-prod
    uses: NBCUniversal/dna-github-actions/.github/workflows/aws-ecs-task-scheduler-deploy-workflow.yml@add_reusable_workflows
    with:
      AWS_ROLE_ARN: arn:aws:iam::************:role/customapps-github-actions-prod-role
      BUILD_COMMAND: echo "build is completed"
      environment: prod
      cloudwatch_event_rule: psiprod-task-scheduler
      container_name: psiprod
      task_definition: psiprod-task-family
      ecr_image: ************.dkr.ecr.us-east-1.amazonaws.com/pam-psi:latest
