# This is a Gradle generated file for dependency locking.
# Manual edits can break the build and are not advised.
# This file is expected to be part of source control.
com.fasterxml.jackson.core:jackson-annotations:2.12.3=classpath
com.fasterxml.jackson.core:jackson-core:2.12.3=classpath
com.fasterxml.jackson.core:jackson-databind:2.12.3=classpath
com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.12.3=classpath
com.fasterxml.jackson.module:jackson-module-parameter-names:2.12.3=classpath
com.fasterxml.jackson:jackson-bom:2.12.3=classpath
com.google.auth:google-auth-library-credentials:0.22.2=classpath
com.google.auth:google-auth-library-oauth2-http:0.22.2=classpath
com.google.auto.value:auto-value-annotations:1.7.4=classpath
com.google.cloud.tools:jib-build-plan:0.4.0=classpath
com.google.cloud.tools:jib-gradle-plugin-extension-api:0.4.0=classpath
com.google.cloud.tools:jib-plugins-extension-common:0.2.0=classpath
com.google.code.findbugs:jsr305:3.0.2=classpath
com.google.errorprone:error_prone_annotations:2.3.4=classpath
com.google.guava:failureaccess:1.0.1=classpath
com.google.guava:guava:30.1-jre=classpath
com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava=classpath
com.google.http-client:google-http-client-apache-v2:1.38.1=classpath
com.google.http-client:google-http-client-jackson2:1.38.0=classpath
com.google.http-client:google-http-client:1.38.1=classpath
com.google.j2objc:j2objc-annotations:1.3=classpath
commons-codec:commons-codec:1.11=classpath
commons-logging:commons-logging:1.2=classpath
gradle.plugin.com.google.cloud.tools:jib-gradle-plugin:2.8.0=classpath
io.grpc:grpc-context:1.22.1=classpath
io.opencensus:opencensus-api:0.24.0=classpath
io.opencensus:opencensus-contrib-http-util:0.24.0=classpath
io.spring.dependency-management:io.spring.dependency-management.gradle.plugin:1.0.11.RELEASE=classpath
io.spring.gradle:dependency-management-plugin:1.0.11.RELEASE=classpath
net.java.dev.jna:jna-platform:5.7.0=classpath
net.java.dev.jna:jna:5.7.0=classpath
org.antlr:antlr4-runtime:4.7.2=classpath
org.apache.commons:commons-compress:1.20=classpath
org.apache.httpcomponents:httpclient:4.5.13=classpath
org.apache.httpcomponents:httpcore:4.4.14=classpath
org.checkerframework:checker-qual:3.5.0=classpath
org.ow2.asm:asm:9.1=classpath
org.springframework.boot:org.springframework.boot.gradle.plugin:2.5.0=classpath
org.springframework.boot:spring-boot-buildpack-platform:2.5.0=classpath
org.springframework.boot:spring-boot-gradle-plugin:2.5.0=classpath
org.springframework.boot:spring-boot-loader-tools:2.5.0=classpath
org.springframework:spring-core:5.3.7=classpath
org.springframework:spring-jcl:5.3.7=classpath
org.tomlj:tomlj:1.0.0=classpath
empty=
