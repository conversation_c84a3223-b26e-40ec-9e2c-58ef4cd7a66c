package com.nbcu.psi;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.Map;

@Tag("unit")
public class ToolTests {

    @Test
    public void testConvertTemplate() {
        final String template = "Hello ${programmingLanguage}";
        final Map<String, Object> map = Collections.singletonMap("programmingLanguage", "Java");
        final String actual = Tool.convertTemplate(template, map);
        Assertions.assertEquals("Hello Java", actual);
    }

    @Test
    public void testJsonParse() throws Exception {
        final String json = "{ \"key1\": \"value1\", \"key2\": \"value2\" }";
        final Map<String, Object> actual = Tool.parseJsonIntoHashMap(json);
        Assertions.assertEquals("value1", actual.get("key1"));
        Assertions.assertEquals("value2", actual.get("key2"));
    }

    @Test
    public void testReadStringFromJson() throws Exception {
        final String json = "{ \"key1\": \"value1\", \"key2\": \"value2\" }";
        final String actual = Tool.readStrFromJson(json, "key1");
        Assertions.assertEquals("value1", actual);
    }

}
