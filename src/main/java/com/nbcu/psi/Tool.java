package com.nbcu.psi;
import java.io.InputStream;
import java.io.File;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.stream.Collectors;
import java.lang.reflect.Field;
import java.util.Collection;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.ManyToMany;
import javax.persistence.OneToOne;



import java.io.StringWriter;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.sql.Timestamp;


import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import com.fasterxml.jackson.databind.ObjectMapper;

public class Tool {
  public static String readResourceFile(String fname) throws Exception {
    Resource resource = new ClassPathResource(fname);
    InputStream inputStream = resource.getInputStream();
    return IOUtils.toString(inputStream, StandardCharsets.UTF_8);
  }

  public static String convertTemplate(String tpl, Map map) {
    return new StringSubstitutor(map, "${", "}").replace(tpl);
  }

  public static String convertTemplate2(String tpl, Map map) {
    return new StringSubstitutor(map, "#{", "}").replace(tpl);
  }

  public static String readStrFromJson(String jsonStr, String key) throws Exception{
    Map<String, Object> map = parseJsonIntoHashMap(jsonStr);
    return (String)(map.get(key));
  }

  public static Map<String, Object> parseJsonIntoHashMap(String jsonStr) throws Exception {
    ObjectMapper om = new ObjectMapper();
    return (Map<String, Object>)(om.readValue(jsonStr, HashMap.class));
  }
  

  public static void pe(Object o) {
    r();
    p(o);
    r();
    System.exit(-1);    
  }

  public static void pr(Object o) {
    p(o);
    r();
  }

  public static void p(Object o) {
    System.out.println(o);
  }

  public static void pf(String format, Object ... args) {
    p(f(format, args));
  }

  public static void r() {
    System.out.println("--------------------");
  }

  public static String f(String format, Object ... args) {
    return String.format(format, args);
  }

  public static String prettyJson(String uglyJsonString) {
    try {
      ObjectMapper mapper = new ObjectMapper();
      Object jsonObject = mapper.readValue(uglyJsonString, Object.class);
      String prettyJson = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(jsonObject);
      return prettyJson;
    } catch(Exception e) {
      return "";
    }
  }

  public static void fileWrite(String fname, String txt) throws Exception {
    File file = new File(fname);
    file.getParentFile().mkdirs();
    FileUtils.writeStringToFile(file, txt, Charset.forName("utf-8"), false);
  }

  public static String fileRead(String fname) throws Exception {
    File file = new File(fname);
    return FileUtils.readFileToString(file, Charset.forName("utf-8"));
  }

  /**
   * Read file and return null if file not found
   */
  public static String fileReadNull(String fname) {
    try {
      return fileRead(fname);
    } catch(Exception e) {
      return null;
    }
  }

  public static void fileWriteNoException(String fname, String txt) {
    try {
      fileWrite(fname, txt);
    } catch(Exception e) {
      //do nothing
    }
  }

  public static String addStringToFilename(String fname, String str) {
    String[] tokens = fname.split("\\.(?=[^\\.]+$)");
    String newFname = tokens[0] + "_" + str + "." + tokens[1];
    return newFname;
  }

  public static String getTimestamp() {
    return new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
  }

  public static String getDatestamp() {
    return new SimpleDateFormat("yyyyMMdd").format(new Date());
  }

  public static String addTimestampToFilename(String fname) {
    return addStringToFilename(fname, getTimestamp());
  }

  public static String getExceptionStr(Throwable e) {
    StringWriter sw = new StringWriter();
    PrintWriter pw = new PrintWriter(sw);
    e.printStackTrace(pw);
    String str = sw.toString(); 
    return str;
  }

  public static void printException(Throwable e) {
    pr(getExceptionStr(e));
  }

  public static String getTimeStr(String format, String tz) {
    SimpleDateFormat fmt = new SimpleDateFormat(format);
    fmt.setTimeZone(TimeZone.getTimeZone(tz)); //This can be adjusted
    return fmt.format(new Date());
  }

  public static String getTimeStr(String tz) {
    return getTimeStr("yyyy-MM-dd HH:mm:ss", tz);
  }

  public static boolean strEmpty(String str) {
    return (str == null || str.isEmpty() || str.trim().isEmpty());
  }

  public static String trimStr(String str) {
    if (str == null) {
      return str;
    }
    return str.trim();
  }

  //1 minute = 60 seconds
  //1 hour = 60 x 60 = 3600
  //1 day = 3600 x 24 = 86400
  public static String timeDiffStr(long startDate, long endDate) {
    //milliseconds
    long different = endDate - startDate;
    
    long secondsInMilli = 1000;
    long minutesInMilli = secondsInMilli * 60;
    long hoursInMilli = minutesInMilli * 60;
    long daysInMilli = hoursInMilli * 24;

    long elapsedDays = different / daysInMilli;
    different = different % daysInMilli;
    
    long elapsedHours = different / hoursInMilli;
    different = different % hoursInMilli;
    
    long elapsedMinutes = different / minutesInMilli;
    different = different % minutesInMilli;
    
    long elapsedSeconds = different / secondsInMilli;
    
    return f("%d days, %d hours, %d minutes, %d seconds%n", 
      elapsedDays, elapsedHours, elapsedMinutes, elapsedSeconds);
  
  }

  public static String escapeJson(String str) {
    return StringEscapeUtils.escapeJson(str);
  }

  public static long bigDecimalToLong(Object o) {
    if (o == null) {
      return 0;
    }
    return ((BigDecimal)o).longValue();
  }

  public static boolean bigDecimalToBoolean(Object o) {
    if (o == null) {
      return false;
    }
    return ((BigDecimal)o).intValue() == 1;
  }

  public static LocalDate timestampToLocalDate(Object o) {
    if (o == null) {
      return null;
    }
    return ((Timestamp)o).toLocalDateTime().toLocalDate();
  }

  public static String idListStr(List<Integer> ids) {
    return ids.stream().map(String::valueOf).collect(Collectors.joining(","));    
  }

  public static boolean isStrBlank(String s) {
    return (s == null || s.isEmpty() || s.trim().isEmpty());
  }
  
  public static String toOneLine(String s) {
    return s.replaceAll("\\R+", " ").trim();
  }

  public static boolean validSfId(String s) {
    return (!isStrBlank(s) && s.length() == 18);
  }

  public static String objToJson(Object obj) {
    ObjectMapper mapper = new ObjectMapper();
    String jsonStr = "";
    try {
        // convert user object to json string and return it 
        jsonStr = mapper.writeValueAsString(obj);
    }
    catch (Exception  e) {
        // catch various errors
        e.printStackTrace();
    }
    return prettyJson(jsonStr);
  }

    /**
     * Converts a date string from "yyyy-MM-dd HH:mm:ss" format to ISO 8601 format.
     * @param dateString The date string in "yyyy-MM-dd HH:mm:ss" format.
     * @return The date string in "yyyy-MM-dd'T'HH:mm:ss'Z'" format.
     * @throws ParseException if the input dateString is not in the expected format.
     */
    public static String convertToIso8601(String dateString) throws Exception {
      SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");

      Date date = inputFormat.parse(dateString);
      return outputFormat.format(date);
  }  

  // Helper method to calculate the maximum width of field names dynamically
  private static int calculateMaxKeyWidth(Field[] fields) {
      int maxWidth = 0;
      for (Field field : fields) {
          maxWidth = Math.max(maxWidth, field.getName().length());
      }
      return maxWidth;
  }
    /**
     * Format a model object for logging without triggering lazy loading.
     * Uses calculateMaxKeyWidth for aligned output.
     */
    public static String formatModel(Object obj) {
      if (obj == null) {
          return "null";
      }

      StringBuffer sb = new StringBuffer();
      sb.append(obj.getClass().getSimpleName()).append("(\n");

      Field[] fields = obj.getClass().getDeclaredFields();
      int maxKeyWidth = calculateMaxKeyWidth(fields);
      boolean first = true;

      for (Field field : fields) {
          // Skip static fields
          if (java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
              continue;
          }

          // Skip JPA relationships to avoid lazy loading
          if (isJpaRelationship(field)) {
              continue;
          }

          field.setAccessible(true);
          
          try {
              Object value = field.get(obj);
              // Only include non-null fields
              if (value != null) {
                  if (!first) {
                      sb.append(",\n");
                  }
                  // Use maxKeyWidth for alignment
                  sb.append("  ").append(String.format("%-" + maxKeyWidth + "s", field.getName()))
                    .append(": ").append(formatFieldValue(value));
                  first = false;
              }
          } catch (IllegalAccessException e) {
              // Skip fields we can't access
              continue;
          }
      }
      
      sb.append("\n)");
      return sb.toString();
  }

    /**
     * Format a single field value for output
     */
    private static String formatFieldValue(Object value) {
      if (value == null) {
          return "null";
      }
      
      if (value instanceof String) {
          return "'" + value + "'";
      }
      
      if (value instanceof Number || value instanceof Boolean) {
          return value.toString();
      }

      if (value instanceof Timestamp) {
          return formatTimestamp((Timestamp)value);
      }
      
      // For other types, just show the class name and hashcode
      return value.getClass().getSimpleName() + "@" + 
             Integer.toHexString(System.identityHashCode(value));
  }

  /**
   * Format timestamp in UTC
   */
  private static String formatTimestamp(Timestamp ts) {
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
      return "'" + sdf.format(ts) + " UTC'";
  }
    /**
     * Check if a field represents a JPA relationship
     */
    private static boolean isJpaRelationship(Field field) {
        return field.isAnnotationPresent(OneToMany.class) ||
               field.isAnnotationPresent(ManyToOne.class) ||
               field.isAnnotationPresent(ManyToMany.class) ||
               field.isAnnotationPresent(OneToOne.class) ||
               Collection.class.isAssignableFrom(field.getType());
    }

}
