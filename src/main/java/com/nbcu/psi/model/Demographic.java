package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.nbcu.psi.Tool;

/**
 * The persistent class for the DEMOGRAPHIC database table.
 * 
 */
@Entity
@NamedQuery(name="Demographic.findAll", query="SELECT d FROM Demographic d")
public class Demographic implements Serializable {
	private static final long serialVersionUID = 1L;

	public String toString() {
		return Tool.f("Demographic(%s %s)", getDemographicId(), getDemographicName());
	}

	@Id
	@Column(name="DEMOGRAPHIC_ID")
	private long demographicId;

	private BigDecimal active;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="DEMOGRAPHIC_NAME")
	private String demographicName;

	@Column(name="DIGITAL_PREFERRED")
	private BigDecimal digitalPreferred;

	@Column(name="DISPLAY_ORDER")
	private BigDecimal displayOrder;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to Budget
	@OneToMany(mappedBy="demographic")
	private List<Budget> budgets;

	//bi-directional many-to-one association to Deal
	@OneToMany(mappedBy="demographic")
	private List<Deal> deals;

	//bi-directional many-to-one association to Property
	@OneToMany(mappedBy="demographic")
	private List<Property> properties;

	public Demographic() {
	}

	public long getDemographicId() {
		return this.demographicId;
	}

	public void setDemographicId(long demographicId) {
		this.demographicId = demographicId;
	}

	public BigDecimal getActive() {
		return this.active;
	}

	public void setActive(BigDecimal active) {
		this.active = active;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public String getDemographicName() {
		return this.demographicName;
	}

	public void setDemographicName(String demographicName) {
		this.demographicName = demographicName;
	}

	public BigDecimal getDigitalPreferred() {
		return this.digitalPreferred;
	}

	public void setDigitalPreferred(BigDecimal digitalPreferred) {
		this.digitalPreferred = digitalPreferred;
	}

	public BigDecimal getDisplayOrder() {
		return this.displayOrder;
	}

	public void setDisplayOrder(BigDecimal displayOrder) {
		this.displayOrder = displayOrder;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public List<Budget> getBudgets() {
		return this.budgets;
	}

	public void setBudgets(List<Budget> budgets) {
		this.budgets = budgets;
	}

	public Budget addBudget(Budget budget) {
		getBudgets().add(budget);
		budget.setDemographic(this);

		return budget;
	}

	public Budget removeBudget(Budget budget) {
		getBudgets().remove(budget);
		budget.setDemographic(null);

		return budget;
	}

	public List<Deal> getDeals() {
		return this.deals;
	}

	public void setDeals(List<Deal> deals) {
		this.deals = deals;
	}

	public Deal addDeal(Deal deal) {
		getDeals().add(deal);
		deal.setDemographic(this);

		return deal;
	}

	public Deal removeDeal(Deal deal) {
		getDeals().remove(deal);
		deal.setDemographic(null);

		return deal;
	}

	public List<Property> getProperties() {
		return this.properties;
	}

	public void setProperties(List<Property> properties) {
		this.properties = properties;
	}

	public Property addProperty(Property property) {
		getProperties().add(property);
		property.setDemographic(this);

		return property;
	}

	public Property removeProperty(Property property) {
		getProperties().remove(property);
		property.setDemographic(null);

		return property;
	}

}