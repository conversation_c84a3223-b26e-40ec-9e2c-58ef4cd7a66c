package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;

import com.nbcu.psi.Tool;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * The persistent class for the ADVERTISER database table.
 * 
 */
@Entity
@NamedQuery(name="Advertiser.findAll", query="SELECT a FROM Advertiser a")
public class Advertiser implements Serializable {
	private static final long serialVersionUID = 1L;

	public String toString() {
		return Tool.f("Advertiser(%s %s)", getAdvertiserId(), getAdvertiserName());
	}

	@Id
	@Column(name="ADVERTISER_ID")
	private long advertiserId;

	private BigDecimal active;

	@Column(name="ADVERTISER_NAME")
	private String advertiserName;

	@Column(name="ADVERTISER_TYPE")
	private String advertiserType;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="D2C_TARGET_ACCOUNT")
	private BigDecimal d2cTargetAccount;

	@Column(name="D2C_TARGET_ACCT_CLASS_ID")
	private BigDecimal d2cTargetAcctClassId;

	@Column(name="DEFAULT_BYCAL")
	private BigDecimal defaultBycal;

	@Column(name="DEFAULT_CATEGORY_ID")
	private BigDecimal defaultCategoryId;

	@Column(name="DISPLAY_ORDER")
	private BigDecimal displayOrder;

	@Column(name="GLOBAL_ACCOUNT")
	private BigDecimal globalAccount;

	@Column(name="MAIN_STATE_ID")
	private BigDecimal mainStateId;

	@Column(name="PARENT_ADVERTISER_ID")
	private BigDecimal parentAdvertiserId;

	@Column(name="PEACOCK_TARGET_ACCOUNT")
	private BigDecimal peacockTargetAccount;

	@Column(name="TARGET_ACCOUNT")
	private BigDecimal targetAccount;

	@Column(name="TARGET_CLASSIFICATION_ID")
	private BigDecimal targetClassificationId;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to Deal
	@OneToMany(mappedBy="advertiser")
	private List<Deal> deals;

	public Advertiser() {
	}

	public long getAdvertiserId() {
		return this.advertiserId;
	}

	public void setAdvertiserId(long advertiserId) {
		this.advertiserId = advertiserId;
	}

	public BigDecimal getActive() {
		return this.active;
	}

	public void setActive(BigDecimal active) {
		this.active = active;
	}

	public String getAdvertiserName() {
		return this.advertiserName;
	}

	public void setAdvertiserName(String advertiserName) {
		this.advertiserName = advertiserName;
	}

	public String getAdvertiserType() {
		return this.advertiserType;
	}

	public void setAdvertiserType(String advertiserType) {
		this.advertiserType = advertiserType;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public BigDecimal getD2cTargetAccount() {
		return this.d2cTargetAccount;
	}

	public void setD2cTargetAccount(BigDecimal d2cTargetAccount) {
		this.d2cTargetAccount = d2cTargetAccount;
	}

	public BigDecimal getD2cTargetAcctClassId() {
		return this.d2cTargetAcctClassId;
	}

	public void setD2cTargetAcctClassId(BigDecimal d2cTargetAcctClassId) {
		this.d2cTargetAcctClassId = d2cTargetAcctClassId;
	}

	public BigDecimal getDefaultBycal() {
		return this.defaultBycal;
	}

	public void setDefaultBycal(BigDecimal defaultBycal) {
		this.defaultBycal = defaultBycal;
	}

	public BigDecimal getDefaultCategoryId() {
		return this.defaultCategoryId;
	}

	public void setDefaultCategoryId(BigDecimal defaultCategoryId) {
		this.defaultCategoryId = defaultCategoryId;
	}

	public BigDecimal getDisplayOrder() {
		return this.displayOrder;
	}

	public void setDisplayOrder(BigDecimal displayOrder) {
		this.displayOrder = displayOrder;
	}

	public BigDecimal getGlobalAccount() {
		return this.globalAccount;
	}

	public void setGlobalAccount(BigDecimal globalAccount) {
		this.globalAccount = globalAccount;
	}

	public BigDecimal getMainStateId() {
		return this.mainStateId;
	}

	public void setMainStateId(BigDecimal mainStateId) {
		this.mainStateId = mainStateId;
	}

	public BigDecimal getParentAdvertiserId() {
		return this.parentAdvertiserId;
	}

	public void setParentAdvertiserId(BigDecimal parentAdvertiserId) {
		this.parentAdvertiserId = parentAdvertiserId;
	}

	public BigDecimal getPeacockTargetAccount() {
		return this.peacockTargetAccount;
	}

	public void setPeacockTargetAccount(BigDecimal peacockTargetAccount) {
		this.peacockTargetAccount = peacockTargetAccount;
	}

	public BigDecimal getTargetAccount() {
		return this.targetAccount;
	}

	public void setTargetAccount(BigDecimal targetAccount) {
		this.targetAccount = targetAccount;
	}

	public BigDecimal getTargetClassificationId() {
		return this.targetClassificationId;
	}

	public void setTargetClassificationId(BigDecimal targetClassificationId) {
		this.targetClassificationId = targetClassificationId;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public List<Deal> getDeals() {
		return this.deals;
	}

	public void setDeals(List<Deal> deals) {
		this.deals = deals;
	}

	public Deal addDeal(Deal deal) {
		getDeals().add(deal);
		deal.setAdvertiser(this);

		return deal;
	}

	public Deal removeDeal(Deal deal) {
		getDeals().remove(deal);
		deal.setAdvertiser(null);

		return deal;
	}

}