package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * The persistent class for the FINANCE_QUARTER database table.
 * 
 */
@Entity
@Table(name="FINANCE_QUARTER")
@NamedQuery(name="FinanceQuarter.findAll", query="SELECT f FROM FinanceQuarter f")
public class FinanceQuarter implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name="FINANCE_QUARTER_ID")
	private long financeQuarterId;

	private BigDecimal active;

	@Column(name="CALENDAR_YEAR_ID")
	private BigDecimal calendarYearId;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="DISABLE_SWING_DATE")
	private BigDecimal disableSwingDate;

	@Column(name="DISPLAY_ORDER")
	private BigDecimal displayOrder;

	@Column(name="FINANCE_METRIC_TYPE_ID")
	private BigDecimal financeMetricTypeId;

	@Column(name="FINANCE_QUARTER")
	private BigDecimal financeQuarter;

	@Column(name="FINANCE_QUARTER_NAME")
	private String financeQuarterName;

	@Column(name="LAST_UPDATED_BY_ID")
	private BigDecimal lastUpdatedById;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to Quarter
	@ManyToOne
	@JoinColumn(name="QUARTER_ID")
	private Quarter quarter;

	//bi-directional many-to-one association to QuarterDate
	@OneToMany(mappedBy="financeQuarter", fetch=FetchType.EAGER, targetEntity=com.nbcu.psi.model.QuarterDate.class)
	private List<QuarterDate> quarterDates;

	// For some reason the app hangs if another OneToMany has the same mappedBy property
	//bi-directional many-to-one association to QuarterDateOverride
	// @OneToMany(mappedBy="financeQuarter", fetch=FetchType.EAGER, targetEntity=com.nbcu.psi.model.QuarterDateOverride.class)
	// private List<QuarterDateOverride> quarterDateOverrides;

	public FinanceQuarter() {
	}

	public long getFinanceQuarterId() {
		return this.financeQuarterId;
	}

	public void setFinanceQuarterId(long financeQuarterId) {
		this.financeQuarterId = financeQuarterId;
	}

	public BigDecimal getActive() {
		return this.active;
	}

	public void setActive(BigDecimal active) {
		this.active = active;
	}

	public BigDecimal getCalendarYearId() {
		return this.calendarYearId;
	}

	public void setCalendarYearId(BigDecimal calendarYearId) {
		this.calendarYearId = calendarYearId;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public BigDecimal getDisableSwingDate() {
		return this.disableSwingDate;
	}

	public void setDisableSwingDate(BigDecimal disableSwingDate) {
		this.disableSwingDate = disableSwingDate;
	}

	public BigDecimal getDisplayOrder() {
		return this.displayOrder;
	}

	public void setDisplayOrder(BigDecimal displayOrder) {
		this.displayOrder = displayOrder;
	}

	public BigDecimal getFinanceMetricTypeId() {
		return this.financeMetricTypeId;
	}

	public void setFinanceMetricTypeId(BigDecimal financeMetricTypeId) {
		this.financeMetricTypeId = financeMetricTypeId;
	}

	public BigDecimal getFinanceQuarter() {
		return this.financeQuarter;
	}

	public void setFinanceQuarter(BigDecimal financeQuarter) {
		this.financeQuarter = financeQuarter;
	}

	public String getFinanceQuarterName() {
		return this.financeQuarterName;
	}

	public void setFinanceQuarterName(String financeQuarterName) {
		this.financeQuarterName = financeQuarterName;
	}

	public BigDecimal getLastUpdatedById() {
		return this.lastUpdatedById;
	}

	public void setLastUpdatedById(BigDecimal lastUpdatedById) {
		this.lastUpdatedById = lastUpdatedById;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public Quarter getQuarter() {
		return this.quarter;
	}

	public void setQuarter(Quarter quarter) {
		this.quarter = quarter;
	}

	public List<QuarterDate> getQuarterDates() {
		return this.quarterDates;
	}

	public void setQuarterDates(List<QuarterDate> quarterDates) {
		this.quarterDates = quarterDates;
	}

	public QuarterDate addQuarterDate(QuarterDate quarterDate) {
		getQuarterDates().add(quarterDate);
		quarterDate.setFinanceQuarter(this);

		return quarterDate;
	}

	public QuarterDate removeQuarterDate(QuarterDate quarterDate) {
		getQuarterDates().remove(quarterDate);
		quarterDate.setFinanceQuarter(null);

		return quarterDate;
	}

	// public List<QuarterDateOverride> getQuarterDateOverrides() {
	// 	return this.quarterDateOverrides;
	// }

	// public List<QuarterDateOverride> getQuarterDateOverridesByPropertyId(Integer propertyId) {
	// 	return this.quarterDateOverrides
	// 						 .stream()
	// 						 .filter(qdo -> qdo.getPropertyId().intValue() == propertyId)
	// 						 .findAny()
	// 						 .map(Collections::singletonList).orElseGet(Collections::emptyList);
	// }

	// public void setQuarterDateOverrides(List<QuarterDateOverride> quarterDateOverrides) {
	// 	this.quarterDateOverrides = quarterDateOverrides;
	// }

	// public QuarterDateOverride addQuarterDateOverride(QuarterDateOverride quarterDateOverride) {
	// 	getQuarterDateOverrides().add(quarterDateOverride);
	// 	quarterDateOverride.setFinanceQuarter(this);

	// 	return quarterDateOverride;
	// }

	// public QuarterDateOverride removeQuarterDateOverride(QuarterDateOverride quarterDateOverride) {
	// 	getQuarterDateOverrides().remove(quarterDateOverride);
	// 	quarterDateOverride.setFinanceQuarter(null);

	// 	return quarterDateOverride;
	// }

}
