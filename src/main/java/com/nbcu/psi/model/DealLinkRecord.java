package com.nbcu.psi.model;
import com.nbcu.psi.Tool;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import com.nbcu.psi.model.QuarterMapping.CyPy;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter @Setter @NoArgsConstructor
public class DealLinkRecord {
  public long dealLinkId;
  public String dealLinkName;
  public long propertyId;
  public long budgetId;
  public long actualPrequarterAmount = 0;
  public long actualQuarter4Amount = 0;
  public long actualQuarter1Amount = 0;
  public long actualQuarter2Amount = 0;
  public long actualQuarter3Amount = 0;
  public long actualPostquarterAmount = 0;
  public long actualAmount = 0;
  public String stage;
  public boolean hasSent = false;
  public String sfdcPropertyId;
  public boolean singleOpportunity;
  public boolean sfSendQuarterly;
  public boolean sfSendPriorYear;
  public boolean sfSendZeroDollars;

  // dayparts will be removed. deals are dayparts.
  List<DealLinkDaypartRecord> dayparts = new ArrayList<DealLinkDaypartRecord>();
  List<DealRecord> deals = new ArrayList<DealRecord>();

  public DealLinkRecord(DealThroughParentDao dao) {
    dealLinkId = dao.getDealLinkId();
    dealLinkName = dao.getDealLinkName();
    stage = dao.getStage();
  }

  public String toString() {
    StringBuffer sb = new StringBuffer();
    sb.append(Tool.f("DealLinkRecord(%s, '%s')\n", 
                  getDealLinkId(),
                  getDealLinkName()));
    for (DealRecord deal: deals) {
      sb.append(Tool.f("\t\t%s\n", deal));
    }
    return sb.toString();
  }

  /**
   * aggregates the quarter values etc.
   */
  public void aggregateDeals() {
    for (DealRecord deal: deals) {
      this.actualPrequarterAmount += deal.getActualPrequarterAmount();
      this.actualPostquarterAmount += deal.getActualPostquarterAmount();
      this.actualQuarter4Amount += deal.getActualQuarter4Amount();
      this.actualQuarter1Amount += deal.getActualQuarter1Amount();
      this.actualQuarter2Amount += deal.getActualQuarter2Amount();
      this.actualQuarter3Amount += deal.getActualQuarter3Amount();
    }
    this.actualAmount = actualPrequarterAmount + actualQuarter4Amount + actualQuarter1Amount + 
      actualQuarter2Amount + actualQuarter3Amount + actualPostquarterAmount;
    DealRecord deal1 = deals.get(0);
    this.dealLinkId = deal1.getDealLinkId();
    this.dealLinkName = deal1.getDealLinkName();
    this.propertyId = deal1.getPropertyId();
    this.budgetId = deal1.getBudgetId();
    this.sfdcPropertyId = deal1.getSfdcPropertyId();
    this.singleOpportunity = deal1.isSingleOpportunity();
    this.sfSendQuarterly = deal1.isSfSendQuarterly();
    this.sfSendPriorYear = deal1.isSfSendPriorYear();
    this.sfSendZeroDollars = deal1.isSfSendZeroDollars();
  }

  /**
   * aggregates the quarter values etc.
   */
  public void aggregate() {
    for (DealLinkDaypartRecord record: dayparts) {
      this.actualPrequarterAmount += record.getActualPrequarterAmount();
      this.actualPostquarterAmount += record.getActualPostquarterAmount();
      this.actualQuarter4Amount += record.getActualQuarter4Amount();
      this.actualQuarter1Amount += record.getActualQuarter1Amount();
      this.actualQuarter2Amount += record.getActualQuarter2Amount();
      this.actualQuarter3Amount += record.getActualQuarter3Amount();
    }
    this.actualAmount = actualPrequarterAmount + actualQuarter4Amount + actualQuarter1Amount + 
      actualQuarter2Amount + actualQuarter3Amount + actualPostquarterAmount;
    DealLinkDaypartRecord record1 = dayparts.get(0);
    this.dealLinkId = record1.getDealLinkId();
    this.dealLinkName = record1.getDealLinkName();
    this.propertyId = record1.getPropertyId();
    this.budgetId = record1.getBudgetId();
    this.sfdcPropertyId = record1.getSfdcPropertyId();
    this.singleOpportunity = record1.isSingleOpportunity();
  }

  public List<Integer> getBudgetIds() {
    return deals.stream().map(s -> (int)s.getBudgetId()).collect(Collectors.toList());
  }

  public List<Integer> getDealIds() {
    return deals.stream().map(s -> (int)s.getDealId()).collect(Collectors.toList());
  }

  public List<Integer> getBudgetIds2() {
    return dayparts.stream().map(s -> (int)s.getBudgetId()).collect(Collectors.toList());
  }

  /**
   * If any budget has sf_deal_id set, then assume the whole deal link has been sent.
   */
  public boolean isAlreadySent() {
    for (DealLinkDaypartRecord daypart: dayparts) {
      if (!Tool.isStrBlank(daypart.getSfDealId())) {
        return true;
      }
    }
    return false;
  }

  public void add(DealLinkDaypartRecord daypart) {
    dayparts.add(daypart);
  }

  public void addDeal(DealRecord deal) {
    deals.add(deal);
  }

}