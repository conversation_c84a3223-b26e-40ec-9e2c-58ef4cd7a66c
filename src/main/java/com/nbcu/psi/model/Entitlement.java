package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * The persistent class for the ENTITLEMENT database table.
 * 
 */
@Entity
@NamedQuery(name="Entitlement.findAll", query="SELECT e FROM Entitlement e")
public class Entitlement implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name="ENTITLEMENT_ID")
	private long entitlementId;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	private String description;

	@Column(name="DISPLAY_ORDER")
	private BigDecimal displayOrder;

	@Column(name="ENTITLEMENT_NAME")
	private String entitlementName;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to RoleEntitlement
	@OneToMany(mappedBy="entitlement", fetch = FetchType.EAGER) // for error: could not initialize proxy - no Session
	private List<RoleEntitlement> roleEntitlements;

	public Entitlement() {
	}

	public long getEntitlementId() {
		return this.entitlementId;
	}

	public void setEntitlementId(long entitlementId) {
		this.entitlementId = entitlementId;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public String getDescription() {
		return this.description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public BigDecimal getDisplayOrder() {
		return this.displayOrder;
	}

	public void setDisplayOrder(BigDecimal displayOrder) {
		this.displayOrder = displayOrder;
	}

	public String getEntitlementName() {
		return this.entitlementName;
	}

	public void setEntitlementName(String entitlementName) {
		this.entitlementName = entitlementName;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public List<RoleEntitlement> getRoleEntitlements() {
		return this.roleEntitlements;
	}

	public void setRoleEntitlements(List<RoleEntitlement> roleEntitlements) {
		this.roleEntitlements = roleEntitlements;
	}

	public RoleEntitlement addRoleEntitlement(RoleEntitlement roleEntitlement) {
		getRoleEntitlements().add(roleEntitlement);
		roleEntitlement.setEntitlement(this);

		return roleEntitlement;
	}

	public RoleEntitlement removeRoleEntitlement(RoleEntitlement roleEntitlement) {
		getRoleEntitlements().remove(roleEntitlement);
		roleEntitlement.setEntitlement(null);

		return roleEntitlement;
	}

}