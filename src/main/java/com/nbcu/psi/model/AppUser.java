package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import com.nbcu.psi.Tool;

/**
 * The persistent class for the APP_USER database table.
 * 
 */
@Entity
@Table(name="APP_USER")
@NamedQuery(name="AppUser.findAll", query="SELECT a FROM AppUser a")
public class AppUser implements Serializable {
	private static final long serialVersionUID = 1L;

	public String toString() {
		return Tool.f("AppUser(%s %s)", getAppUserId(), getFirstName());
	}

	@Id
	@Column(name="APP_USER_ID")
	private long appUserId;

	@Column(name="ACCOUNT_EXECUTIVE")
	private BigDecimal accountExecutive;

	private BigDecimal active;

	@Column(name="APP_USER_DEPARTMENT_ID")
	private BigDecimal appUserDepartmentId;

	@Column(name="APP_USER_TITLE_ID")
	private BigDecimal appUserTitleId;

	@Column(name="CELL_PHONE_NUMBER")
	private String cellPhoneNumber;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	private String email;

	@Column(name="EXTERNAL_USER")
	private BigDecimal externalUser;

	@Column(name="FIRST_NAME")
	private String firstName;

	@Temporal(TemporalType.DATE)
	@Column(name="LAST_ACTIVE_AT")
	private Date lastActiveAt;

	@Column(name="LAST_NAME")
	private String lastName;

	@Column(name="LOCATION_ID")
	private BigDecimal locationId;

	@Column(name="MASKED_NAME")
	private String maskedName;

	@Column(name="PHONE_NUMBER")
	private String phoneNumber;

	@Column(name="SSO_ID")
	private BigDecimal ssoId;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	public AppUser() {
	}

	public long getAppUserId() {
		return this.appUserId;
	}

	public void setAppUserId(long appUserId) {
		this.appUserId = appUserId;
	}

	public BigDecimal getAccountExecutive() {
		return this.accountExecutive;
	}

	public void setAccountExecutive(BigDecimal accountExecutive) {
		this.accountExecutive = accountExecutive;
	}

	public BigDecimal getActive() {
		return this.active;
	}

	public void setActive(BigDecimal active) {
		this.active = active;
	}

	public BigDecimal getAppUserDepartmentId() {
		return this.appUserDepartmentId;
	}

	public void setAppUserDepartmentId(BigDecimal appUserDepartmentId) {
		this.appUserDepartmentId = appUserDepartmentId;
	}

	public BigDecimal getAppUserTitleId() {
		return this.appUserTitleId;
	}

	public void setAppUserTitleId(BigDecimal appUserTitleId) {
		this.appUserTitleId = appUserTitleId;
	}

	public String getCellPhoneNumber() {
		return this.cellPhoneNumber;
	}

	public void setCellPhoneNumber(String cellPhoneNumber) {
		this.cellPhoneNumber = cellPhoneNumber;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public BigDecimal getExternalUser() {
		return this.externalUser;
	}

	public void setExternalUser(BigDecimal externalUser) {
		this.externalUser = externalUser;
	}

	public String getFirstName() {
		return this.firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public Date getLastActiveAt() {
		return this.lastActiveAt;
	}

	public void setLastActiveAt(Date lastActiveAt) {
		this.lastActiveAt = lastActiveAt;
	}

	public String getLastName() {
		return this.lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public BigDecimal getLocationId() {
		return this.locationId;
	}

	public void setLocationId(BigDecimal locationId) {
		this.locationId = locationId;
	}

	public String getMaskedName() {
		return this.maskedName;
	}

	public void setMaskedName(String maskedName) {
		this.maskedName = maskedName;
	}

	public String getPhoneNumber() {
		return this.phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public BigDecimal getSsoId() {
		return this.ssoId;
	}

	public void setSsoId(BigDecimal ssoId) {
		this.ssoId = ssoId;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

}
