package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;


/**
 * The persistent class for the QUARTER_DATE_OVERRIDE database table.
 * 
 */
@Entity
@Table(name="QUARTER_DATE_OVERRIDE")
@NamedQuery(name="QuarterDateOverride.findAll", query="SELECT q FROM QuarterDateOverride q")
public class QuarterDateOverride implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name="QUARTER_DATE_OVERRIDE_ID")
	private long quarterDateOverrideId;

	private BigDecimal active;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="DISPLAY_ORDER")
	private BigDecimal displayOrder;

	@Column(name="LAST_UPDATED_BY_ID")
	private BigDecimal lastUpdatedById;

	@Column(name="POST_SWING_DATE")
	private String postSwingDate;

	@Temporal(TemporalType.DATE)
	@Column(name="POST_SWING_END_DATE")
	private Date postSwingEndDate;

	@Temporal(TemporalType.DATE)
	@Column(name="POST_SWING_START_DATE")
	private Date postSwingStartDate;

	@Column(name="PROPERTY_ID")
	private BigDecimal propertyId;

	@Column(name="QUARTER_DATE")
	private String quarterDate;

	@Column(name="QUARTER_END_DATE")
	private LocalDate quarterEndDate;

	@Column(name="QUARTER_START_DATE")
	private LocalDate quarterStartDate;

	@Column(name="SWING_DATE")
	private String swingDate;

	@Temporal(TemporalType.DATE)
	@Column(name="SWING_END_DATE")
	private Date swingEndDate;

	@Temporal(TemporalType.DATE)
	@Column(name="SWING_START_DATE")
	private Date swingStartDate;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to FinanceQuarter
	@ManyToOne
	@JoinColumn(name="FINANCE_QUARTER_ID")
	private FinanceQuarter financeQuarter;

	public QuarterDateOverride() {
	}

	public long getQuarterDateOverrideId() {
		return this.quarterDateOverrideId;
	}

	public void setQuarterDateOverrideId(long quarterDateOverrideId) {
		this.quarterDateOverrideId = quarterDateOverrideId;
	}

	public BigDecimal getActive() {
		return this.active;
	}

	public void setActive(BigDecimal active) {
		this.active = active;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public BigDecimal getDisplayOrder() {
		return this.displayOrder;
	}

	public void setDisplayOrder(BigDecimal displayOrder) {
		this.displayOrder = displayOrder;
	}

	public BigDecimal getLastUpdatedById() {
		return this.lastUpdatedById;
	}

	public void setLastUpdatedById(BigDecimal lastUpdatedById) {
		this.lastUpdatedById = lastUpdatedById;
	}

	public String getPostSwingDate() {
		return this.postSwingDate;
	}

	public void setPostSwingDate(String postSwingDate) {
		this.postSwingDate = postSwingDate;
	}

	public Date getPostSwingEndDate() {
		return this.postSwingEndDate;
	}

	public void setPostSwingEndDate(Date postSwingEndDate) {
		this.postSwingEndDate = postSwingEndDate;
	}

	public Date getPostSwingStartDate() {
		return this.postSwingStartDate;
	}

	public void setPostSwingStartDate(Date postSwingStartDate) {
		this.postSwingStartDate = postSwingStartDate;
	}

	public BigDecimal getPropertyId() {
		return this.propertyId;
	}

	public void setPropertyId(BigDecimal propertyId) {
		this.propertyId = propertyId;
	}

	public String getQuarterDate() {
		return this.quarterDate;
	}

	public void setQuarterDate(String quarterDate) {
		this.quarterDate = quarterDate;
	}

	public LocalDate getQuarterEndDate() {
		return this.quarterEndDate;
	}

	public void setQuarterEndDate(LocalDate quarterEndDate) {
		this.quarterEndDate = quarterEndDate;
	}

	public LocalDate getQuarterStartDate() {
		return this.quarterStartDate;
	}

	public void setQuarterStartDate(LocalDate quarterStartDate) {
		this.quarterStartDate = quarterStartDate;
	}

	public String getSwingDate() {
		return this.swingDate;
	}

	public void setSwingDate(String swingDate) {
		this.swingDate = swingDate;
	}

	public Date getSwingEndDate() {
		return this.swingEndDate;
	}

	public void setSwingEndDate(Date swingEndDate) {
		this.swingEndDate = swingEndDate;
	}

	public Date getSwingStartDate() {
		return this.swingStartDate;
	}

	public void setSwingStartDate(Date swingStartDate) {
		this.swingStartDate = swingStartDate;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public FinanceQuarter getFinanceQuarter() {
		return this.financeQuarter;
	}

	public void setFinanceQuarter(FinanceQuarter financeQuarter) {
		this.financeQuarter = financeQuarter;
	}

}
