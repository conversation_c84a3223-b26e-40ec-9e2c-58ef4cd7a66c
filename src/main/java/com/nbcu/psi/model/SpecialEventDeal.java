package com.nbcu.psi.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import com.nbcu.psi.Tool;

@Entity
@Table(name = "SPECIAL_EVENT_DEAL")
@Getter @Setter
public class SpecialEventDeal implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @Id
    @Column(name="SPECIAL_EVENT_DEAL_ID")
    private long specialEventDealId;

    @Column(name="AGENCY_ID")
    private long agencyId;
    
    @Column(name="ADVERTISER_ID") 
    private long advertiserId;
    
    @Column(name="BUYING_APP_USER_ID")
    private long buyingAppUserId;
    
    @Column(name="CLIENT_APP_USER_ID")
    private long clientAppUserId;
    
    @Column(name="PLANNING_APP_USER_ID")
    private long planningAppUserId;

    @Column(name="PLANNER_APP_USER_ID")
    private long plannerAppUserId;

    @Column(name="LINEAR_PLANNER_APP_USER_ID")
    private long linearPlannerAppUserId;

    @Column(name="DIGITAL_PLANNER_APP_USER_ID")
    private long digitalPlannerAppUserId;
    
    @Column(name="ACCOUNT_MANAGER_APP_USER_ID")
    private long accountManagerAppUserId;
    
    @Column(name="DEMOGRAPHIC_ID")
    private long demographicId;
    
    @Column(name="DEAL_TAG_ID")
    private long dealTagId;
    
    @Column(name="PRODUCT_CATEGORY_ID")
    private long productCategoryId;
    
    @Column(name="SALES_TYPE_ID")
    private long salesTypeId;
    
    @Column(name="SPONSORSHIP_TYPE_ID")
    private long sponsorshipTypeId;
    
    @Column(name="SPONSORSHIP_DETAILS")
    private String sponsorshipDetails;
    
    @Column(name="DEAL_NAME")
    private String dealName;
    
    @Column(name="RATING_STREAM_ID")
    private long ratingStreamId;
    
    @Column(name="STATUS_ID")
    private long statusId;
    
    @Column(name="MARKETPLACE_ID")
    private long marketplaceId;
    
    @ManyToOne
    @JoinColumn(name="MARKETPLACE_ID", insertable=false, updatable=false)
    private Marketplace marketplace;
    
    @ManyToOne
    @JoinColumn(name="PLANNER_APP_USER_ID", insertable=false, updatable=false)
    private AppUser plannerAppUser;
    
    @ManyToOne
    @JoinColumn(name="LINEAR_PLANNER_APP_USER_ID", insertable=false, updatable=false)
    private AppUser linearPlannerAppUser;

    @ManyToOne
    @JoinColumn(name="DIGITAL_PLANNER_APP_USER_ID", insertable=false, updatable=false)
    private AppUser digitalPlannerAppUser;

    @ManyToOne
    @JoinColumn(name="PLANNING_APP_USER_ID", insertable=false, updatable=false)
    private AppUser planningAppUser;
    
    @ManyToOne
    @JoinColumn(name="ACCOUNT_MANAGER_APP_USER_ID", insertable=false, updatable=false)
    private AppUser accountManagerAppUser;
    
    @OneToMany(mappedBy = "specialEventDeal")
    private List<SpecialEventDealDetail> dealDetails;

    @Override
    public String toString() {
        return Tool.f("SpecialEventDeal(id=%d, name=%s, agency=%d, advertiser=%d)", 
            specialEventDealId,
            dealName,
            agencyId,
            advertiserId);
    }
}