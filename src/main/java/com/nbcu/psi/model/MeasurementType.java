package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.nbcu.psi.Tool;

/**
 * The persistent class for the MEASUREMENT_TYPE database table.
 * 
 */
@Entity
@Table(name="MEASUREMENT_TYPE")
@NamedQuery(name="MeasurementType.findAll", query="SELECT m FROM MeasurementType m")
public class MeasurementType implements Serializable {
	private static final long serialVersionUID = 1L;

  public String toString() {
		return Tool.f("MeasurementType(%s %s)", getMeasurementTypeId(), getMeasurementTypeName());
	}

	@Id
	@Column(name="MEASUREMENT_TYPE_ID")
	private long measurementTypeId;

	private BigDecimal active;

	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="DISPLAY_ORDER")
	private BigDecimal displayOrder;

	@Column(name="MEASUREMENT_TYPE_NAME")
	private String measurementTypeName;

	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to Budget
	@OneToMany(mappedBy="measurementType")
	private List<Budget> budgets;

	//bi-directional many-to-one association to Deal
	@OneToMany(mappedBy="measurementType")
	private List<Deal> deals;

	// //bi-directional many-to-one association to Property
	// @OneToMany(mappedBy="measurementType")
	// private List<Property> properties;

	public MeasurementType() {
	}

	public long getMeasurementTypeId() {
		return this.measurementTypeId;
	}

	public void setMeasurementTypeId(long measurementTypeId) {
		this.measurementTypeId = measurementTypeId;
	}

	public BigDecimal getActive() {
		return this.active;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public BigDecimal getDisplayOrder() {
		return this.displayOrder;
	}

	public String getMeasurementTypeName() {
		return this.measurementTypeName;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public List<Budget> getBudgets() {
		return this.budgets;
	}

	public List<Deal> getDeals() {
		return this.deals;
	}

	// public List<Property> getProperties() {
	// 	return this.properties;
	// }

}