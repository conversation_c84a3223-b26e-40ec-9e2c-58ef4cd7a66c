package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.util.Date;
import java.util.List;


/**
 * The persistent class for the APP_ROLE database table.
 * 
 */
@Entity
@Table(name="APP_ROLE")
@NamedQuery(name="AppRole.findAll", query="SELECT a FROM AppRole a")
public class AppRole implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name="APP_ROLE_ID")
	private long appRoleId;

	@Column(name="APP_ROLE_NAME")
	private String appRoleName;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to RoleEntitlement
	@OneToMany(mappedBy="appRole")
	private List<RoleEntitlement> roleEntitlements;

	public AppRole() {
	}

	public long getAppRoleId() {
		return this.appRoleId;
	}

	public void setAppRoleId(long appRoleId) {
		this.appRoleId = appRoleId;
	}

	public String getAppRoleName() {
		return this.appRoleName;
	}

	public void setAppRoleName(String appRoleName) {
		this.appRoleName = appRoleName;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public List<RoleEntitlement> getRoleEntitlements() {
		return this.roleEntitlements;
	}

	public void setRoleEntitlements(List<RoleEntitlement> roleEntitlements) {
		this.roleEntitlements = roleEntitlements;
	}

	public RoleEntitlement addRoleEntitlement(RoleEntitlement roleEntitlement) {
		getRoleEntitlements().add(roleEntitlement);
		roleEntitlement.setAppRole(this);

		return roleEntitlement;
	}

	public RoleEntitlement removeRoleEntitlement(RoleEntitlement roleEntitlement) {
		getRoleEntitlements().remove(roleEntitlement);
		roleEntitlement.setAppRole(null);

		return roleEntitlement;
	}

}