package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.nbcu.psi.Tool;

/**
 * The persistent class for the MARKETPLACE database table.
 * 
 */
@Entity
@NamedQuery(name="Marketplace.findAll", query="SELECT m FROM Marketplace m")
public class Marketplace implements Serializable {
	private static final long serialVersionUID = 1L;

	public String toString() {
		return Tool.f("Marketplace(%s %s)", getMarketplaceId(), getMarketplaceName());
	}

	@Id
	@Column(name="MARKETPLACE_ID")
	private long marketplaceId;

	private BigDecimal active;

	@Column(name="ACTIVE_ON_AGENCY_GATEWAY")
	private BigDecimal activeOnAgencyGateway;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="DEFAULT_MARKETPLACE")
	private BigDecimal defaultMarketplace;

	@Column(name="DISPLAY_ORDER")
	private BigDecimal displayOrder;

	@Column(name="MARKETPLACE_NAME")
	private String marketplaceName;

	@Column(name="MARKETPLACE_TYPE")
	private String marketplaceType;

	@Column(name="SHOW_IN_PACING")
	private BigDecimal showInPacing;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to Budget
	@OneToMany(mappedBy="marketplace")
	private List<Budget> budgets;

	//bi-directional many-to-one association to Deal
	@OneToMany(mappedBy="marketplace")
	private List<Deal> deals;

	public Marketplace() {
	}

	public long getMarketplaceId() {
		return this.marketplaceId;
	}

	public void setMarketplaceId(long marketplaceId) {
		this.marketplaceId = marketplaceId;
	}

	public BigDecimal getActive() {
		return this.active;
	}

	public void setActive(BigDecimal active) {
		this.active = active;
	}

	public BigDecimal getActiveOnAgencyGateway() {
		return this.activeOnAgencyGateway;
	}

	public void setActiveOnAgencyGateway(BigDecimal activeOnAgencyGateway) {
		this.activeOnAgencyGateway = activeOnAgencyGateway;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public BigDecimal getDefaultMarketplace() {
		return this.defaultMarketplace;
	}

	public void setDefaultMarketplace(BigDecimal defaultMarketplace) {
		this.defaultMarketplace = defaultMarketplace;
	}

	public BigDecimal getDisplayOrder() {
		return this.displayOrder;
	}

	public void setDisplayOrder(BigDecimal displayOrder) {
		this.displayOrder = displayOrder;
	}

	public String getMarketplaceName() {
		return this.marketplaceName;
	}

	public void setMarketplaceName(String marketplaceName) {
		this.marketplaceName = marketplaceName;
	}

	public String getMarketplaceType() {
		return this.marketplaceType;
	}

	public void setMarketplaceType(String marketplaceType) {
		this.marketplaceType = marketplaceType;
	}

	public BigDecimal getShowInPacing() {
		return this.showInPacing;
	}

	public void setShowInPacing(BigDecimal showInPacing) {
		this.showInPacing = showInPacing;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public List<Budget> getBudgets() {
		return this.budgets;
	}

	public void setBudgets(List<Budget> budgets) {
		this.budgets = budgets;
	}

	public Budget addBudget(Budget budget) {
		getBudgets().add(budget);
		budget.setMarketplace(this);

		return budget;
	}

	public Budget removeBudget(Budget budget) {
		getBudgets().remove(budget);
		budget.setMarketplace(null);

		return budget;
	}

	public List<Deal> getDeals() {
		return this.deals;
	}

	public void setDeals(List<Deal> deals) {
		this.deals = deals;
	}

	public Deal addDeal(Deal deal) {
		getDeals().add(deal);
		deal.setMarketplace(this);

		return deal;
	}

	public Deal removeDeal(Deal deal) {
		getDeals().remove(deal);
		deal.setMarketplace(null);

		return deal;
	}
}
