package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.util.Date;

/**
 * Simplified AdvertiserBrand model with relationship to Advertiser.
 */
@Entity
@Table(name = "ADVERTISER_BRAND")
public class AdvertiserBrand implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ADVERTISER_BRAND_ID", nullable = false)
    private Long advertiserBrandId;

    @Column(name = "ADVERTISER_BRAND_NAME")
    private String advertiserBrandName;

    @ManyToOne(fetch = FetchType.LAZY) // Defines a many-to-one relationship
    @JoinColumn(name = "ADVERTISER_ID", nullable = false, insertable = false, updatable = false)
    private Advertiser advertiser; // Assuming there is an Advertiser entity

    @Column(name = "ACTIVE", nullable = false)
    private Boolean active;

    @Column(name = "DISPLAY_ORDER")
    private Long displayOrder;

    @Column(name = "LAST_UPDATED_BY_ID", nullable = false)
    private Long lastUpdatedById;

    @Column(name = "CREATED_AT", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Column(name = "UPDATED_AT", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    // Getters and Setters
    public Long getAdvertiserBrandId() {
        return advertiserBrandId;
    }

    public void setAdvertiserBrandId(Long advertiserBrandId) {
        this.advertiserBrandId = advertiserBrandId;
    }

    public String getAdvertiserBrandName() {
        return advertiserBrandName;
    }

    public void setAdvertiserBrandName(String advertiserBrandName) {
        this.advertiserBrandName = advertiserBrandName;
    }

    public Advertiser getAdvertiser() { // Modified to work with the Advertiser entity
        return advertiser;
    }

    public void setAdvertiser(Advertiser advertiser) { // Modified to accept an Advertiser entity
        this.advertiser = advertiser;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public Long getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(Long displayOrder) {
        this.displayOrder = displayOrder;
    }

    public Long getLastUpdatedById() {
        return lastUpdatedById;
    }

    public void setLastUpdatedById(Long lastUpdatedById) {
        this.lastUpdatedById = lastUpdatedById;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public String toString() {
        return "AdvertiserBrand{" +
               "advertiserBrandId=" + advertiserBrandId +
               ", advertiserBrandName='" + advertiserBrandName + '\'' +
               ", advertiser=" + (advertiser != null ? advertiser.getAdvertiserId() : "null") + // Use advertiser ID for toString to avoid loading the advertiser entity
               ", active=" + active +
               ", displayOrder=" + displayOrder +
               ", lastUpdatedById=" + lastUpdatedById +
               ", createdAt=" + createdAt +
               ", updatedAt=" + updatedAt +
               '}';
    }
}
