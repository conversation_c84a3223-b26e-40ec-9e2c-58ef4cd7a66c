package com.nbcu.psi.model;

import ch.qos.logback.core.joran.action.ActionUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter @Setter @NoArgsConstructor
public class DealLinkDaypartRecord {
  public long dealLinkId;
  public String dealLinkName;
  public boolean singleOpportunity;
  public long propertyId;
  public long budgetId;
  public long actualPrequarterAmount;
  public long actualQuarter4Amount;
  public long actualQuarter1Amount;
  public long actualQuarter2Amount;
  public long actualQuarter3Amount;
  public long actualPostquarterAmount;
  public String sfdcVerticalName;
  public String sfdcVerticalId;
  public String sfdcPropertyId;
  public String sfDealId;
  public String sfOpportunityId;

  public DealLinkDaypartRecord(DealThroughParentDao dao) {
    dealLinkId = dao.getDealLinkId();
    dealLinkName = dao.getDealLinkName();
    singleOpportunity = dao.isSingleOpportunity();
    propertyId = dao.getPropertyId();
    budgetId = dao.getBudgetId();
    actualPrequarterAmount = dao.getActualPrequarterAmount();
    actualQuarter4Amount = dao.getActualQuarter4Amount();
    actualQuarter1Amount = dao.getActualQuarter1Amount();
    actualQuarter2Amount = dao.getActualQuarter2Amount();
    actualQuarter3Amount = dao.getActualQuarter3Amount();
    actualPostquarterAmount = dao.getActualPostquarterAmount();
    sfdcVerticalName = dao.getSfdcVerticalName();
    sfdcVerticalId = dao.getSfdcVerticalId();
    sfdcPropertyId = dao.getSfdcPropertyId();
    sfDealId = dao.getSfDealId();
    sfOpportunityId = dao.getSfOpportunityId();
  }

  public String toFullString() {
    return "DealLinkDaypartRecord{" +
        "dealLinkId=" + dealLinkId +
        ", dealLinkName='" + dealLinkName + '\'' +
        ", singleOpportunity=" + singleOpportunity +
        ", propertyId=" + propertyId +
        ", budgetId=" + budgetId +
        ", actualPrequarterAmount=" + actualPrequarterAmount +
        ", actualQuarter4Amount=" + actualQuarter4Amount +
        ", actualQuarter1Amount=" + actualQuarter1Amount +
        ", actualQuarter2Amount=" + actualQuarter2Amount +
        ", actualQuarter3Amount=" + actualQuarter3Amount +
        ", actualPostquarterAmount=" + actualPostquarterAmount +
        ", sfdcVerticalName='" + sfdcVerticalName + '\'' +
        ", sfdcVerticalId='" + sfdcVerticalId + '\'' +
        ", sfdcPropertyId='" + sfdcPropertyId + '\'' +
        ", sfDealId='" + sfDealId + '\'' +
        ", sfOpportunityId='" + sfOpportunityId + '\'' +
        '}';
  }
}
