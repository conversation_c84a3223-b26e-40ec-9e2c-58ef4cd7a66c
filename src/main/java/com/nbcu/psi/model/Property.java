package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.nbcu.psi.Tool;

/**
 * The persistent class for the PROPERTY database table.
 * 
 */
@Entity
@NamedQuery(name="Property.findAll", query="SELECT p FROM Property p")
public class Property implements Serializable {
	private static final long serialVersionUID = 1L;

	public String toString() {
		return Tool.f("Property(%s %s)", getPropertyId(), getPropertyName());
	}

	@Id
	@Column(name="PROPERTY_ID")
	private long propertyId;

	@Column(name="ABOVE_THE_LINE_ID")
	private BigDecimal aboveTheLineId;

	private BigDecimal active;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="DISPLAY_ORDER")
	private BigDecimal displayOrder;

	@Column(name="DIVISION_ID")
	private BigDecimal divisionId;

	private String email;

	@Column(name="IMAGE_FILE_NAME")
	private String imageFileName;

	@Column(name="INCLUDE_FINANCE_MODEL")
	private BigDecimal includeFinanceModel;

	@Column(name="INCLUDE_NEGOTIATION_TRACKER")
	private BigDecimal includeNegotiationTracker;

	@Column(name="INCLUDE_REMIX")
	private BigDecimal includeRemix;

	@Column(name="INCLUDE_TAD")
	private BigDecimal includeTad;

	@Column(name="OPTIMIZATION_TYPE_ID")
	private BigDecimal optimizationTypeId;

	@Column(name="PROPERTY_GROUP_ID")
	private BigDecimal propertyGroupId;

	@Column(name="PROPERTY_NAME")
	private String propertyName;

	@Column(name="SHOW_IN_PACING")
	private BigDecimal showInPacing;

	@Column(name="SUBDIVISION_ID")
	private BigDecimal subdivisionId;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to Deal
	@OneToMany(mappedBy="property")
	private List<Deal> deals;

	//bi-directional many-to-one association to Demographic
	@ManyToOne
	@JoinColumn(name="DEMOGRAPHIC_ID")
	private Demographic demographic;

	//bi-directional many-to-one association to MeasurementType
	@ManyToOne
	@JoinColumn(name="MEASUREMENT_TYPE_ID")
	private MeasurementType measurementType;

	//bi-directional many-to-one association to Pillar
	@ManyToOne
	@JoinColumn(name="PILLAR_ID")
	private Pillar pillar;

	//bi-directional many-to-one association to SellingVertical
	@ManyToOne
	@JoinColumn(name="SELLING_VERTICAL_ID")
	private SellingVertical sellingVertical;

	//bi-directional many-to-one association to PropertyType
	@ManyToOne
	@JoinColumn(name="PROPERTY_TYPE_ID")
	private PropertyType propertyType;

	//bi-directional many-to-one association to Vertical
	@ManyToOne
	@JoinColumn(name="VERTICAL_ID")
	private Vertical vertical;

	public Property() {
	}

	public long getPropertyId() {
		return this.propertyId;
	}

	public void setPropertyId(long propertyId) {
		this.propertyId = propertyId;
	}

	public BigDecimal getAboveTheLineId() {
		return this.aboveTheLineId;
	}

	public void setAboveTheLineId(BigDecimal aboveTheLineId) {
		this.aboveTheLineId = aboveTheLineId;
	}

	public BigDecimal getActive() {
		return this.active;
	}

	public void setActive(BigDecimal active) {
		this.active = active;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public BigDecimal getDisplayOrder() {
		return this.displayOrder;
	}

	public void setDisplayOrder(BigDecimal displayOrder) {
		this.displayOrder = displayOrder;
	}

	public BigDecimal getDivisionId() {
		return this.divisionId;
	}

	public void setDivisionId(BigDecimal divisionId) {
		this.divisionId = divisionId;
	}

	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getImageFileName() {
		return this.imageFileName;
	}

	public void setImageFileName(String imageFileName) {
		this.imageFileName = imageFileName;
	}

	public BigDecimal getIncludeFinanceModel() {
		return this.includeFinanceModel;
	}

	public void setIncludeFinanceModel(BigDecimal includeFinanceModel) {
		this.includeFinanceModel = includeFinanceModel;
	}

	public BigDecimal getIncludeNegotiationTracker() {
		return this.includeNegotiationTracker;
	}

	public void setIncludeNegotiationTracker(BigDecimal includeNegotiationTracker) {
		this.includeNegotiationTracker = includeNegotiationTracker;
	}

	public BigDecimal getIncludeRemix() {
		return this.includeRemix;
	}

	public void setIncludeRemix(BigDecimal includeRemix) {
		this.includeRemix = includeRemix;
	}

	public BigDecimal getIncludeTad() {
		return this.includeTad;
	}

	public void setIncludeTad(BigDecimal includeTad) {
		this.includeTad = includeTad;
	}

	public BigDecimal getOptimizationTypeId() {
		return this.optimizationTypeId;
	}

	public void setOptimizationTypeId(BigDecimal optimizationTypeId) {
		this.optimizationTypeId = optimizationTypeId;
	}

	public BigDecimal getPropertyGroupId() {
		return this.propertyGroupId;
	}

	public void setPropertyGroupId(BigDecimal propertyGroupId) {
		this.propertyGroupId = propertyGroupId;
	}

	public String getPropertyName() {
		return this.propertyName;
	}

	public void setPropertyName(String propertyName) {
		this.propertyName = propertyName;
	}

	public BigDecimal getShowInPacing() {
		return this.showInPacing;
	}

	public void setShowInPacing(BigDecimal showInPacing) {
		this.showInPacing = showInPacing;
	}

	public BigDecimal getSubdivisionId() {
		return this.subdivisionId;
	}

	public void setSubdivisionId(BigDecimal subdivisionId) {
		this.subdivisionId = subdivisionId;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public List<Deal> getDeals() {
		return this.deals;
	}

	public void setDeals(List<Deal> deals) {
		this.deals = deals;
	}

	public Deal addDeal(Deal deal) {
		getDeals().add(deal);
		deal.setProperty(this);

		return deal;
	}

	public Deal removeDeal(Deal deal) {
		getDeals().remove(deal);
		deal.setProperty(null);

		return deal;
	}
	
	public Demographic getDemographic() {
		return this.demographic;
	}

	public void setDemographic(Demographic demographic) {
		this.demographic = demographic;
	}

	public Pillar getPillar() {
		return this.pillar;
	}

	public void setPillar(Pillar pillar) {
		this.pillar = pillar;
	}

	public SellingVertical getSellingVertical() {
		return this.sellingVertical;
	}

	public void setSellingVertical(SellingVertical sellingVertical) {
		this.sellingVertical = sellingVertical;
	}

	public PropertyType getPropertyType() {
		return this.propertyType;
	}

	public void setPropertyType(PropertyType propertyType) {
		this.propertyType = propertyType;
	}

	public MeasurementType getMeasurementType() {
		return this.measurementType;
	}

	public Vertical getVertical() {
		return this.vertical;
	}

	public void setVertical(Vertical vertical) {
		this.vertical = vertical;
	}

}