package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import com.nbcu.psi.Tool;

/**
 * The persistent class for the PRODUCT_CATEGORY database table.
 */
@Entity
@Table(name="PRODUCT_CATEGORY")
@NamedQuery(name="ProductCategory.findAll", query="SELECT p FROM ProductCategory p")
public class ProductCategory implements Serializable {
    private static final long serialVersionUID = 1L;

    public String toString() {
        return Tool.f("ProductCategory(%s %s)", getProductCategoryId(), getProductCategoryName());
    }

    @Id
    @Column(name="PRODUCT_CATEGORY_ID")
    private long productCategoryId;

    @Column(name="PRODUCT_CATEGORY_NAME")
    private String productCategoryName;

    @Temporal(TemporalType.DATE)
    @Column(name="CREATED_AT")
    private Date createdAt;

    @Temporal(TemporalType.DATE)
    @Column(name="UPDATED_AT")
    private Date updatedAt;

    @Column(name="DISPLAY_ORDER")
    private BigDecimal displayOrder;

    @Column(name="LAST_UPDATED_BY_ID")
    private BigDecimal lastUpdatedById;

    public long getProductCategoryId() {
        return this.productCategoryId;
    }

    public void setProductCategoryId(long productCategoryId) {
        this.productCategoryId = productCategoryId;
    }

    public String getProductCategoryName() {
        return this.productCategoryName;
    }

    public void setProductCategoryName(String productCategoryName) {
        this.productCategoryName = productCategoryName;
    }

    public Date getCreatedAt() {
        return this.createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public BigDecimal getDisplayOrder() {
        return this.displayOrder;
    }

    public void setDisplayOrder(BigDecimal displayOrder) {
        this.displayOrder = displayOrder;
    }

    public BigDecimal getLastUpdatedById() {
        return this.lastUpdatedById;
    }

    public void setLastUpdatedById(BigDecimal lastUpdatedById) {
        this.lastUpdatedById = lastUpdatedById;
    }
}