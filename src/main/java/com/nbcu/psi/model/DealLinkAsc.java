package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * The persistent class for the DEAL_LINK_ASC database table.
 * 
 */
@Entity
@Table(name="DEAL_LINK_ASC")
@NamedQuery(name="DealLinkAsc.findAll", query="SELECT d FROM DealLinkAsc d")
public class DealLinkAsc implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name="DEAL_LINK_ASC_ID")
	private long dealLinkAscId;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to Deal
	@ManyToOne
	@JoinColumn(name="DEAL_ID")
	private Deal deal;

	//bi-directional many-to-one association to DealLink
	@ManyToOne
	@JoinColumn(name="DEAL_LINK_ID")
	private DealLink dealLink;

	public DealLinkAsc() {
	}

	public long getDealLinkAscId() {
		return this.dealLinkAscId;
	}

	public void setDealLinkAscId(long dealLinkAscId) {
		this.dealLinkAscId = dealLinkAscId;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public Deal getDeal() {
		return this.deal;
	}

	public void setDeal(Deal deal) {
		this.deal = deal;
	}

	public DealLink getDealLink() {
		return this.dealLink;
	}

	public void setDealLink(DealLink dealLink) {
		this.dealLink = dealLink;
	}

}