package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.nbcu.psi.Tool;

/**
 * The persistent class for the DEAL database table.
 * 
 */
@Entity
@NamedQuery(name="Deal.findAll", query="SELECT d FROM Deal d")
public class Deal implements Serializable {
	private static final long serialVersionUID = 1L;

	public String toString() {
		return Tool.f("Deal(%s %s)", getDealId(), getProperty().getPropertyName());
		// return Tool.f("Deal(%s)", getDealId());
	}

	@Id
	@Column(name="DEAL_ID")
	private long dealId;

	@Column(name="ADVERTISER_BRAND_ID")
	private BigDecimal advertiserBrandId;

    // bi-directional many-to-one association to AdvertiserBrand
    @ManyToOne
    @JoinColumn(name="ADVERTISER_BRAND_ID", referencedColumnName = "ADVERTISER_BRAND_ID", insertable = false, updatable = false)
    private AdvertiserBrand advertiserBrand; // Adds a relationship to AdvertiserBrand

	private BigDecimal bycal;

	@Column(name="CONTENT_ID")
	private BigDecimal contentId;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="DEAL_NAME")
	private String dealName;

	@Column(name="DEAL_TAG_ID")
	private BigDecimal dealTagId;

	// @Column(name="\"GLOBAL\"")
	// private BigDecimal global;

	// @Column(name="INCLUDE_IN_BASELINE")
	// private BigDecimal includeInBaseline;

	@Column(name="MULTI_YEAR")
	private BigDecimal multiYear;

	@Column(name="MULTI_YEAR_DEAL_LENGTH")
	private BigDecimal multiYearDealLength;

	@Column(name="PARTNERSHIP_ID")
	private BigDecimal partnershipId;

	private BigDecimal placeholder;

	private BigDecimal placeholder1;

	@Column(name="PLACEHOLDER2_ID")
	private BigDecimal placeholder2Id;

	private String placeholder3;

	// Doesn't exists on dev
	// @Column(name="PLAN_STATUS")
	// private String planStatus;

	@Column(name="PRODUCT_CATEGORY_ID")
	private BigDecimal productCategoryId;

	@Column(name="PROGRAMMATIC_ELIGIBLE")
	private BigDecimal programmaticEligible;

	@Column(name="REGISTRATION_TYPE_ID")
	private BigDecimal registrationTypeId;

	@Column(name="SALES_TYPE_ID")
	private BigDecimal salesTypeId;

	private String source;

	@Column(name="SPONSORSHIP_DETAILS")
	private String sponsorshipDetails;

	@Column(name="SPONSORSHIP_TYPE_ID")
	private BigDecimal sponsorshipTypeId;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to Vertical
	@ManyToOne
	@JoinColumn(name="VERTICAL_ID")
	private Vertical vertical;

	//bi-directional many-to-one association to Budget
	@OneToMany(mappedBy="deal", fetch = FetchType.EAGER) // for error: could not initialize proxy - no Session
	// @OneToMany(mappedBy="deal")
	private List<Budget> budgets;

	//bi-directional many-to-one association to Advertiser
	@ManyToOne
	@JoinColumn(name="ADVERTISER_ID")
	private Advertiser advertiser;

	//bi-directional many-to-one association to Agency
	@ManyToOne
	@JoinColumn(name="AGENCY_ID")
	private Agency agency;

	//bi-directional many-to-one association to AppUser
	@ManyToOne
	@JoinColumn(name="APP_USER_ID")
	private AppUser appUser2;

	//bi-directional many-to-one association to AppUser
	@ManyToOne
	@JoinColumn(name="PLANNER_APP_USER_ID")
	private AppUser appUser3;

	//bi-directional many-to-one association to AppUser
	@ManyToOne
	@JoinColumn(name="CAE_APP_USER_ID")
	private AppUser appUser4;

	//bi-directional many-to-one association to AppUser
	@ManyToOne
	@JoinColumn(name="ACCOUNT_MANAGER_APP_USER_ID")
	private AppUser accountManagerAppUser;

	//bi-directional many-to-one association to Demographic
	@ManyToOne
	@JoinColumn(name="DEMOGRAPHIC_ID")
	private Demographic demographic;

	//bi-directional many-to-one association to RatingSteam
	@ManyToOne
	@JoinColumn(name="RATING_STREAM_ID")
	private RatingStream ratingStream;

	//bi-directional many-to-one association to Marketplace
	@ManyToOne
	@JoinColumn(name="MARKETPLACE_ID")
	private Marketplace marketplace;

	//bi-directional many-to-one association to Property
	@ManyToOne
	@JoinColumn(name="PROPERTY_ID")
	private Property property;

	//bi-directional many-to-one association to Currency
	@ManyToOne
	@JoinColumn(name="CURRENCY_ID")
	private Currency currency;

	//bi-directional many-to-one association to MeasurementType
	@ManyToOne
	@JoinColumn(name="MEASUREMENT_TYPE_ID")
	private MeasurementType measurementType;

	public Deal() {
	}

	public long getDealId() {
		return this.dealId;
	}

	public void setDealId(long dealId) {
		this.dealId = dealId;
	}

	public BigDecimal getAdvertiserBrandId() {
		return this.advertiserBrandId;
	}

	public void setAdvertiserBrandId(BigDecimal advertiserBrandId) {
		this.advertiserBrandId = advertiserBrandId;
	}

	public BigDecimal getBycal() {
		return this.bycal;
	}

	public void setBycal(BigDecimal bycal) {
		this.bycal = bycal;
	}

	public BigDecimal getContentId() {
		return this.contentId;
	}

	public void setContentId(BigDecimal contentId) {
		this.contentId = contentId;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public String getDealName() {
		return this.dealName;
	}

	public void setDealName(String dealName) {
		this.dealName = dealName;
	}

	public BigDecimal getDealTagId() {
		return this.dealTagId;
	}

	public void setDealTagId(BigDecimal dealTagId) {
		this.dealTagId = dealTagId;
	}

	public BigDecimal getMultiYear() {
		return this.multiYear;
	}

	public void setMultiYear(BigDecimal multiYear) {
		this.multiYear = multiYear;
	}

	public BigDecimal getMultiYearDealLength() {
		return this.multiYearDealLength;
	}

	public void setMultiYearDealLength(BigDecimal multiYearDealLength) {
		this.multiYearDealLength = multiYearDealLength;
	}

	public BigDecimal getPartnershipId() {
		return this.partnershipId;
	}

	public void setPartnershipId(BigDecimal partnershipId) {
		this.partnershipId = partnershipId;
	}

	public BigDecimal getPlaceholder() {
		return this.placeholder;
	}

	public void setPlaceholder(BigDecimal placeholder) {
		this.placeholder = placeholder;
	}

	public BigDecimal getPlaceholder1() {
		return this.placeholder1;
	}

	public void setPlaceholder1(BigDecimal placeholder1) {
		this.placeholder1 = placeholder1;
	}

	public BigDecimal getPlaceholder2Id() {
		return this.placeholder2Id;
	}

	public void setPlaceholder2Id(BigDecimal placeholder2Id) {
		this.placeholder2Id = placeholder2Id;
	}

	public String getPlaceholder3() {
		return this.placeholder3;
	}

	public void setPlaceholder3(String placeholder3) {
		this.placeholder3 = placeholder3;
	}

	public BigDecimal getProductCategoryId() {
		return this.productCategoryId;
	}

	public void setProductCategoryId(BigDecimal productCategoryId) {
		this.productCategoryId = productCategoryId;
	}

	public BigDecimal getProgrammaticEligible() {
		return this.programmaticEligible;
	}

	public void setProgrammaticEligible(BigDecimal programmaticEligible) {
		this.programmaticEligible = programmaticEligible;
	}

	public BigDecimal getRegistrationTypeId() {
		return this.registrationTypeId;
	}

	public void setRegistrationTypeId(BigDecimal registrationTypeId) {
		this.registrationTypeId = registrationTypeId;
	}

	public BigDecimal getSalesTypeId() {
		return this.salesTypeId;
	}

	public void setSalesTypeId(BigDecimal salesTypeId) {
		this.salesTypeId = salesTypeId;
	}

	public String getSource() {
		return this.source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getSponsorshipDetails() {
		return this.sponsorshipDetails;
	}

	public void setSponsorshipDetails(String sponsorshipDetails) {
		this.sponsorshipDetails = sponsorshipDetails;
	}

	public BigDecimal getSponsorshipTypeId() {
		return this.sponsorshipTypeId;
	}

	public void setSponsorshipTypeId(BigDecimal sponsorshipTypeId) {
		this.sponsorshipTypeId = sponsorshipTypeId;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public Vertical getVertical() {
		return this.vertical;
	}

	public void setVertical(Vertical vertical) {
		this.vertical = vertical;
	}

	public List<Budget> getBudgets() {
		return this.budgets;
	}

	public void setBudgets(List<Budget> budgets) {
		this.budgets = budgets;
	}

	public Budget addBudget(Budget budget) {
		getBudgets().add(budget);
		budget.setDeal(this);

		return budget;
	}

	public Budget removeBudget(Budget budget) {
		getBudgets().remove(budget);
		budget.setDeal(null);

		return budget;
	}

	public Advertiser getAdvertiser() {
		return this.advertiser;
	}

	public void setAdvertiser(Advertiser advertiser) {
		this.advertiser = advertiser;
	}

	public Agency getAgency() {
		return this.agency;
	}

	public void setAgency(Agency agency) {
		this.agency = agency;
	}

	public AppUser getAppUser2() {
		return this.appUser2;
	}

	public void setAppUser2(AppUser appUser2) {
		this.appUser2 = appUser2;
	}

	public AppUser getAppUser3() {
		return this.appUser3;
	}

	public void setAppUser3(AppUser appUser3) {
		this.appUser3 = appUser3;
	}

	public AppUser getAppUser4() {
		return this.appUser4;
	}

	public void setAppUser4(AppUser appUser4) {
		this.appUser4 = appUser4;
	}

	public AppUser getAccountManagerAppUser() {
		return this.accountManagerAppUser;
	}

	public void setAccountManagerAppUser(AppUser accountManagerAppUser) {
		this.accountManagerAppUser = accountManagerAppUser;
	}

	public Demographic getDemographic() {
		return this.demographic;
	}

	public void setDemographic(Demographic demographic) {
		this.demographic = demographic;
	}

	public RatingStream getRatingStream() {
		return this.ratingStream;
	}

	public void setRatingStream(RatingStream ratingStream) {
		this.ratingStream = ratingStream;
	}

	public Marketplace getMarketplace() {
		return this.marketplace;
	}

	public void setMarketplace(Marketplace marketplace) {
		this.marketplace = marketplace;
	}

	public Property getProperty() {
		return this.property;
	}

	public void setProperty(Property property) {
		this.property = property;
	}

	public Currency getCurrency() {
		return this.currency;
	}

	public MeasurementType getMeasurementType() {
		return this.measurementType;
	}

	// Existing getters and setters...

	public AdvertiserBrand getAdvertiserBrand() {
		return this.advertiserBrand;
	}

	public void setAdvertiserBrand(AdvertiserBrand advertiserBrand) {
		this.advertiserBrand = advertiserBrand;
	}

	// More existing getters and setters...
	

}