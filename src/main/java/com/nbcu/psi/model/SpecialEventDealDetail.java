package com.nbcu.psi.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import com.nbcu.psi.Tool;

@Entity
@Table(name = "SPECIAL_EVENT_DEAL_DETAIL")
@Getter @Setter
public class SpecialEventDealDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name="SPECIAL_EVENT_DEAL_DETAIL_ID")
    private long specialEventDealDetailId;
    
    @Column(name="SPECIAL_EVENT_DEAL_ID")
    private long specialEventDealId;
    
    @Column(name="PROPERTY_ID") 
    private long propertyId;
    
    @Column(name="BUDGET_DOLLARS")
    private BigDecimal budgetDollars;
    
    @Column(name="CONFIDENCE_LEVEL_ID")
    private long confidenceLevelId;
    
    @Column(name="SPECIAL_EVENT_ID")
    private long specialEventId;
    
    @Column(name="SF_OPPORTUNITY_ID")
    private String sfOpportunityId;
    
    @Column(name="SF_DEAL_ID")
    private String sfDealId;
    
    @Column(name="SF_SYNC_AT")
    private Date sfSyncAt;
    
    @Column(name="SF_SYNC_STATUS")
    private String sfSyncStatus;

    @ManyToOne
    @JoinColumn(name="SPECIAL_EVENT_DEAL_ID", insertable=false, updatable=false)
    private SpecialEventDeal specialEventDeal;

    @ManyToOne
    @JoinColumn(name="SPECIAL_EVENT_ID", insertable=false, updatable=false) 
    private SpecialEvent specialEvent;
    
    @Override
    public String toString() {
        return Tool.f("SpecialEventDealDetail(id=%d, dealId=%d, eventId=%d, property=%d, budget=%.2f, status=%s)",
            specialEventDealDetailId,
            specialEventDealId, 
            specialEventId,
            propertyId,
            budgetDollars != null ? budgetDollars.doubleValue() : 0.0,
            sfSyncStatus);
    }
}