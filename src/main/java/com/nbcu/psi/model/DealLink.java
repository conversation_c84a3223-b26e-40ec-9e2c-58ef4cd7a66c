package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * The persistent class for the DEAL_LINK database table.
 * 
 */
@Entity
@Table(name="DEAL_LINK")
@NamedQuery(name="DealLink.findAll", query="SELECT d FROM DealLink d")
public class DealLink implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name="DEAL_LINK_ID")
	private long dealLinkId;

	private BigDecimal active;

	@Column(name="ACTUAL_POSTQUARTER_AMOUNT")
	private BigDecimal actualPostquarterAmount;

	@Column(name="ACTUAL_PREQUARTER_AMOUNT")
	private BigDecimal actualPrequarterAmount;

	@Column(name="ACTUAL_QUARTER1_AMOUNT")
	private BigDecimal actualQuarter1Amount;

	@Column(name="ACTUAL_QUARTER2_AMOUNT")
	private BigDecimal actualQuarter2Amount;

	@Column(name="ACTUAL_QUARTER3_AMOUNT")
	private BigDecimal actualQuarter3Amount;

	@Column(name="ACTUAL_QUARTER4_AMOUNT")
	private BigDecimal actualQuarter4Amount;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="DEAL_LINK_NAME")
	private String dealLinkName;

	@Column(name="DISPLAY_ORDER")
	private BigDecimal displayOrder;

	@Temporal(TemporalType.DATE)
	@Column(name="END_DATE")
	private Date endDate;

	@Temporal(TemporalType.DATE)
	@Column(name="START_DATE")
	private Date startDate;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to Advertiser
	@ManyToOne
	@JoinColumn(name="ADVERTISER_ID")
	private Advertiser advertiser;

	//bi-directional many-to-one association to Agency
	@ManyToOne
	@JoinColumn(name="AGENCY_ID")
	private Agency agency;

	//bi-directional many-to-one association to AppUser
	@ManyToOne
	@JoinColumn(name="LAST_UPDATED_BY_ID")
	private AppUser appUser1;

	//bi-directional many-to-one association to AppUser
	@ManyToOne
	@JoinColumn(name="CAE_APP_USER_ID")
	private AppUser appUser2;

	//bi-directional many-to-one association to AppUser
	@ManyToOne
	@JoinColumn(name="APP_USER_ID")
	private AppUser appUser3;

	//bi-directional many-to-one association to DealLinkType
	@ManyToOne
	@JoinColumn(name="DEAL_LINK_TYPE_ID")
	private DealLinkType dealLinkType;

	//bi-directional many-to-one association to Demographic
	@ManyToOne
	@JoinColumn(name="DEMOGRAPHIC_ID")
	private Demographic demographic;

	//bi-directional many-to-one association to Marketplace
	@ManyToOne
	@JoinColumn(name="MARKETPLACE_ID")
	private Marketplace marketplace;

	//bi-directional many-to-one association to RatingStream
	@ManyToOne
	@JoinColumn(name="RATING_STREAM_ID")
	private RatingStream ratingStream;

	//bi-directional many-to-one association to DealLinkAsc
	@OneToMany(mappedBy="dealLink")
	private List<DealLinkAsc> dealLinkAscs;

	public DealLink() {
	}

	public long getDealLinkId() {
		return this.dealLinkId;
	}

	public void setDealLinkId(long dealLinkId) {
		this.dealLinkId = dealLinkId;
	}

	public BigDecimal getActive() {
		return this.active;
	}

	public void setActive(BigDecimal active) {
		this.active = active;
	}

	public BigDecimal getActualPostquarterAmount() {
		return this.actualPostquarterAmount;
	}

	public void setActualPostquarterAmount(BigDecimal actualPostquarterAmount) {
		this.actualPostquarterAmount = actualPostquarterAmount;
	}

	public BigDecimal getActualPrequarterAmount() {
		return this.actualPrequarterAmount;
	}

	public void setActualPrequarterAmount(BigDecimal actualPrequarterAmount) {
		this.actualPrequarterAmount = actualPrequarterAmount;
	}

	public BigDecimal getActualQuarter1Amount() {
		return this.actualQuarter1Amount;
	}

	public void setActualQuarter1Amount(BigDecimal actualQuarter1Amount) {
		this.actualQuarter1Amount = actualQuarter1Amount;
	}

	public BigDecimal getActualQuarter2Amount() {
		return this.actualQuarter2Amount;
	}

	public void setActualQuarter2Amount(BigDecimal actualQuarter2Amount) {
		this.actualQuarter2Amount = actualQuarter2Amount;
	}

	public BigDecimal getActualQuarter3Amount() {
		return this.actualQuarter3Amount;
	}

	public void setActualQuarter3Amount(BigDecimal actualQuarter3Amount) {
		this.actualQuarter3Amount = actualQuarter3Amount;
	}

	public BigDecimal getActualQuarter4Amount() {
		return this.actualQuarter4Amount;
	}

	public void setActualQuarter4Amount(BigDecimal actualQuarter4Amount) {
		this.actualQuarter4Amount = actualQuarter4Amount;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public String getDealLinkName() {
		return this.dealLinkName;
	}

	public void setDealLinkName(String dealLinkName) {
		this.dealLinkName = dealLinkName;
	}

	public BigDecimal getDisplayOrder() {
		return this.displayOrder;
	}

	public void setDisplayOrder(BigDecimal displayOrder) {
		this.displayOrder = displayOrder;
	}

	public Date getEndDate() {
		return this.endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Date getStartDate() {
		return this.startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public Advertiser getAdvertiser() {
		return this.advertiser;
	}

	public void setAdvertiser(Advertiser advertiser) {
		this.advertiser = advertiser;
	}

	public Agency getAgency() {
		return this.agency;
	}

	public void setAgency(Agency agency) {
		this.agency = agency;
	}

	public AppUser getAppUser1() {
		return this.appUser1;
	}

	public void setAppUser1(AppUser appUser1) {
		this.appUser1 = appUser1;
	}

	public AppUser getAppUser2() {
		return this.appUser2;
	}

	public void setAppUser2(AppUser appUser2) {
		this.appUser2 = appUser2;
	}

	public AppUser getAppUser3() {
		return this.appUser3;
	}

	public void setAppUser3(AppUser appUser3) {
		this.appUser3 = appUser3;
	}

	public DealLinkType getDealLinkType() {
		return this.dealLinkType;
	}

	public void setDealLinkType(DealLinkType dealLinkType) {
		this.dealLinkType = dealLinkType;
	}

	public Demographic getDemographic() {
		return this.demographic;
	}

	public void setDemographic(Demographic demographic) {
		this.demographic = demographic;
	}

	public Marketplace getMarketplace() {
		return this.marketplace;
	}

	public void setMarketplace(Marketplace marketplace) {
		this.marketplace = marketplace;
	}

	public RatingStream getRatingStream() {
		return this.ratingStream;
	}

	public void setRatingStream(RatingStream ratingStream) {
		this.ratingStream = ratingStream;
	}

	public List<DealLinkAsc> getDealLinkAscs() {
		return this.dealLinkAscs;
	}
}