package com.nbcu.psi.model;
import com.nbcu.psi.Tool;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import com.nbcu.psi.model.QuarterMapping.CyPy;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter @Setter @NoArgsConstructor
public class DealRecord {
  public long dealId;
  public String dealName;
  public long budgetId;
  public long dealLinkId;
  public String dealLinkName;
  public long propertyId;
  public long actualPrequarterAmount = 0;
  public long actualQuarter4Amount = 0;
  public long actualQuarter1Amount = 0;
  public long actualQuarter2Amount = 0;
  public long actualQuarter3Amount = 0;
  public long actualPostquarterAmount = 0;
  public String stage;
  public long actualAmount = 0;
  public boolean singleOpportunity;
  public String sfdcVerticalId;
  public String sfdcVerticalName;
  public String sfdcPropertyId;
  public String sfOpportunityId;
  public String sfDealId;
  public boolean sfSendQuarterly;
  public boolean sfSendPriorYear;
  public boolean sfSendZeroDollars;
  public boolean hasSent = false;
  public boolean placeholder = false;

  public DealRecord(DealThroughParentDao dao) {
    dealId = dao.getDealId();
    dealName = dao.getDealName();
    budgetId = dao.getBudgetId();
    dealLinkId = dao.getDealLinkId();
    dealLinkName = dao.getDealLinkName();
    propertyId = dao.getPropertyId();
    stage = dao.getStage();
    singleOpportunity = dao.isSingleOpportunity();
    sfdcVerticalId = dao.getSfdcVerticalId();
    sfdcVerticalName = dao.getSfdcVerticalName();
    sfdcPropertyId = dao.getSfdcPropertyId();
    sfOpportunityId = dao.getSfOpportunityId();
    sfDealId = dao.getSfDealId();
    sfSendQuarterly = dao.isSfSendQuarterly();
    sfSendPriorYear = dao.isSfSendPriorYear();
    sfSendZeroDollars = dao.isSfSendZeroDollars();
    actualPrequarterAmount = dao.getPrequarterAmount();
    actualQuarter4Amount = dao.getQuarter4Amount();
    actualQuarter1Amount = dao.getQuarter1Amount();
    actualQuarter2Amount = dao.getQuarter2Amount();
    actualQuarter3Amount = dao.getQuarter3Amount();
    actualPostquarterAmount = dao.getPostquarterAmount();

    actualAmount = actualPrequarterAmount + actualQuarter4Amount + actualQuarter1Amount + 
      actualQuarter2Amount + actualQuarter3Amount + actualPostquarterAmount;
    placeholder = dao.isPlaceholder();
  }

  /**
   * implements the sfSendZeroDollars logic.
   * In DealThroughParentDao we already set 0 dollar for place hold, we also set dollar based on isSfSendPriorYear
   * So the logic here is simple:
   * if sfSendZeroDollars is true, then send 
   * if placeholder is true, then send (already set to 0)
   * if actualAmount > 0 , then send (already taking care of isSfSendPriorYear)
   * @return
   */
  public boolean shouldSend() {
    return sfSendZeroDollars || placeholder || actualAmount > 0;
  }

  public long placeholderAmount(long amount) {
    if (placeholder) {
      return 0L;
    } else {
      return amount;
    }
  }
  
  public String toString() {
    return Tool.f("DealRecord(%s, '%s')", 
                  getDealId(),
                  getDealName());
  }

  public String toFullString() {
    return Tool.f("DealRecord(dealId=%s, dealName=%s, budgetId=%s, dealLinkId=%s, "
            + "dealLinkName=%s, propertyId=%s, actualPrequarterAmount=%s, "
            + "actualQuarter4Amount=%s, actualQuarter1Amount=%s, actualQuarter2Amount=%s, "
            + "actualQuarter3Amount=%s, actualPostquarterAmount=%s, stage=%s, "
            + "actualAmount=%s, singleOpportunity=%s, sfdcVerticalId=%s, "
            + "sfdcVerticalName=%s, sfdcPropertyId=%s, sfOpportunityId=%s, sfDealId=%s, "
            + "sfSendQuarterly=%s, sfSendPriorYear=%s, sfSendZeroDollars=%s, hasSent=%s, "
            + "placeholder=%s)", 
            dealId, dealName, budgetId, dealLinkId, dealLinkName, propertyId, 
            actualPrequarterAmount, actualQuarter4Amount, actualQuarter1Amount, 
            actualQuarter2Amount, actualQuarter3Amount, actualPostquarterAmount, stage, 
            actualAmount, singleOpportunity, sfdcVerticalId, sfdcVerticalName, 
            sfdcPropertyId, sfOpportunityId, sfDealId, sfSendQuarterly, sfSendPriorYear, 
            sfSendZeroDollars, hasSent, placeholder);
  }

  /**
   * If any budget has sf_deal_id set, then assume the whole deal link has been sent.
   */
  public boolean isAlreadySent() {
    return Tool.isStrBlank(getSfDealId());
  }

  //may need to use deal.java to calculate quarter date

}