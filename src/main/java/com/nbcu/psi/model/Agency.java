package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.nbcu.psi.Tool;

/**
 * The persistent class for the AGENCY database table.
 * 
 */
@Entity
@NamedQuery(name="Agency.findAll", query="SELECT a FROM Agency a")
public class Agency implements Serializable {
	private static final long serialVersionUID = 1L;

	public String toString() {
		return Tool.f("Agency(%s %s)", getAgencyId(), getAgencyName());
	}

	@Id
	@Column(name="AGENCY_ID")
	private long agencyId;

	private BigDecimal active;

	@Column(name="AGENCY_NAME")
	private String agencyName;

	@Column(name="AGENCY_TYPE")
	private String agencyType;

	private BigDecimal barter;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="DISPLAY_ORDER")
	private BigDecimal displayOrder;

	private String email;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to Agency
	@ManyToOne
	@JoinColumn(name="PARENT_AGENCY_ID")
	private Agency agency;

	//bi-directional many-to-one association to Agency
	@OneToMany(mappedBy="agency")
	private List<Agency> agencies;

	//bi-directional many-to-one association to Budget
	@OneToMany(mappedBy="agency")
	private List<Budget> budgets;

	//bi-directional many-to-one association to Deal
	@OneToMany(mappedBy="agency")
	private List<Deal> deals;

	public Agency() {
	}

	public long getAgencyId() {
		return this.agencyId;
	}

	public void setAgencyId(long agencyId) {
		this.agencyId = agencyId;
	}

	public BigDecimal getActive() {
		return this.active;
	}

	public void setActive(BigDecimal active) {
		this.active = active;
	}

	public String getAgencyName() {
		return this.agencyName;
	}

	public void setAgencyName(String agencyName) {
		this.agencyName = agencyName;
	}

	public String getAgencyType() {
		return this.agencyType;
	}

	public void setAgencyType(String agencyType) {
		this.agencyType = agencyType;
	}

	public BigDecimal getBarter() {
		return this.barter;
	}

	public void setBarter(BigDecimal barter) {
		this.barter = barter;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public BigDecimal getDisplayOrder() {
		return this.displayOrder;
	}

	public void setDisplayOrder(BigDecimal displayOrder) {
		this.displayOrder = displayOrder;
	}

	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public Agency getAgency() {
		return this.agency;
	}

	public void setAgency(Agency agency) {
		this.agency = agency;
	}

	public List<Agency> getAgencies() {
		return this.agencies;
	}

	public void setAgencies(List<Agency> agencies) {
		this.agencies = agencies;
	}

	public Agency addAgency(Agency agency) {
		getAgencies().add(agency);
		agency.setAgency(this);

		return agency;
	}

	public Agency removeAgency(Agency agency) {
		getAgencies().remove(agency);
		agency.setAgency(null);

		return agency;
	}

	public List<Budget> getBudgets() {
		return this.budgets;
	}

	public void setBudgets(List<Budget> budgets) {
		this.budgets = budgets;
	}

	public Budget addBudget(Budget budget) {
		getBudgets().add(budget);
		budget.setAgency(this);

		return budget;
	}

	public Budget removeBudget(Budget budget) {
		getBudgets().remove(budget);
		budget.setAgency(null);

		return budget;
	}

	public List<Deal> getDeals() {
		return this.deals;
	}

	public void setDeals(List<Deal> deals) {
		this.deals = deals;
	}

	public Deal addDeal(Deal deal) {
		getDeals().add(deal);
		deal.setAgency(this);

		return deal;
	}

	public Deal removeDeal(Deal deal) {
		getDeals().remove(deal);
		deal.setAgency(null);

		return deal;
	}

}