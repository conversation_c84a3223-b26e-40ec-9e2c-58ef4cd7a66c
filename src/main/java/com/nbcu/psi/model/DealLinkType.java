package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * The persistent class for the DEAL_LINK_TYPE database table.
 * 
 */
@Entity
@Table(name="DEAL_LINK_TYPE")
@NamedQuery(name="DealLinkType.findAll", query="SELECT d FROM DealLinkType d")
public class DealLinkType implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name="DEAL_LINK_TYPE_ID")
	private long dealLinkTypeId;

	private BigDecimal active;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="DEAL_LINK_TYPE_NAME")
	private String dealLinkTypeName;

	@Column(name="DISPLAY_ORDER")
	private BigDecimal displayOrder;

	@Column(name="SEND_TO_SALESFORCE")
	private BigDecimal sendToSalesforce;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to DealLink
	@OneToMany(mappedBy="dealLinkType")
	private List<DealLink> dealLinks;

	public DealLinkType() {
	}

	public long getDealLinkTypeId() {
		return this.dealLinkTypeId;
	}

	public void setDealLinkTypeId(long dealLinkTypeId) {
		this.dealLinkTypeId = dealLinkTypeId;
	}

	public BigDecimal getActive() {
		return this.active;
	}

	public void setActive(BigDecimal active) {
		this.active = active;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public String getDealLinkTypeName() {
		return this.dealLinkTypeName;
	}

	public void setDealLinkTypeName(String dealLinkTypeName) {
		this.dealLinkTypeName = dealLinkTypeName;
	}

	public BigDecimal getDisplayOrder() {
		return this.displayOrder;
	}

	public void setDisplayOrder(BigDecimal displayOrder) {
		this.displayOrder = displayOrder;
	}

	public BigDecimal getSendToSalesforce() {
		return this.sendToSalesforce;
	}

	public void setSendToSalesforce(BigDecimal sendToSalesforce) {
		this.sendToSalesforce = sendToSalesforce;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public List<DealLink> getDealLinks() {
		return this.dealLinks;
	}
}