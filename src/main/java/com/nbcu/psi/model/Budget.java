package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import com.nbcu.psi.Tool;
import com.nbcu.psi.model.QuarterMapping.CyPy;

/**
 * The persistent class for the BUDGET database table.
 * 
 */
@Entity
@NamedQuery(name="Budget.findAll", query="SELECT b FROM Budget b")
public class Budget implements Serializable {
	private static final long serialVersionUID = 1L;

	public String toString() {
		return Tool.f("Budget(%s %s '%d' '%s')", getBudgetId(), getBudgetYear().getBudgetYearName(), getSendToSalesforce().intValue(), getSfOpportunityId());
	}

	@Id
	@Column(name="BUDGET_ID")
	private long budgetId;

	@Column(name="ACTUAL_POSTQUARTER_AMOUNT")
	private BigDecimal actualPostquarterAmount;

	@Column(name="ACTUAL_PREQUARTER_AMOUNT")
	private BigDecimal actualPrequarterAmount;

	@Column(name="ACTUAL_QUARTER1_AMOUNT")
	private BigDecimal actualQuarter1Amount;

	@Column(name="ACTUAL_QUARTER2_AMOUNT")
	private BigDecimal actualQuarter2Amount;

	@Column(name="ACTUAL_QUARTER3_AMOUNT")
	private BigDecimal actualQuarter3Amount;

	@Column(name="ACTUAL_QUARTER4_AMOUNT")
	private BigDecimal actualQuarter4Amount;

	@Column(name="AGENCY_COUNTER_ROC")
	private BigDecimal agencyCounterRoc;

	@Column(name="ASK_POSTQUARTER_AMOUNT")
	private BigDecimal askPostquarterAmount;

	@Column(name="ASK_PREQUARTER_AMOUNT")
	private BigDecimal askPrequarterAmount;

	@Column(name="ASK_QUARTER1_AMOUNT")
	private BigDecimal askQuarter1Amount;

	@Column(name="ASK_QUARTER2_AMOUNT")
	private BigDecimal askQuarter2Amount;

	@Column(name="ASK_QUARTER3_AMOUNT")
	private BigDecimal askQuarter3Amount;

	@Column(name="ASK_QUARTER4_AMOUNT")
	private BigDecimal askQuarter4Amount;

	@Column(name="CONFIDENCE_LEVEL_ID")
	private BigDecimal confidenceLevelId;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="GUARANTEED_IMPRESSIONS")
	private BigDecimal guaranteedImpressions;

	@Column(name="HH_IMPRESSIONS")
	private BigDecimal hhImpressions;

	@Column(name="NEW_BUSINESS_PRICING")
	private BigDecimal newBusinessPricing;

	@Column(name="NOT_RETURNING_CUSTOM_REASON")
	private String notReturningCustomReason;

	@Column(name="NOT_RETURNING_REASON_ID")
	private BigDecimal notReturningReasonId;

	@Temporal(TemporalType.DATE)
	@Column(name="OPTIONABLE_DATE")
	private Date optionableDate;

	@Column(name="P_18_49_IMPRESSIONS")
	private BigDecimal p1849Impressions;

	@Column(name="P_25_54_IMPRESSIONS")
	private BigDecimal p2554Impressions;

	@Column(name="P18_PLUS_IMPRESSIONS")
	private BigDecimal p18PlusImpressions;

	@Column(name="P2_PLUS_IMPRESSIONS")
	private BigDecimal p2PlusImpressions;

	@Column(name="PLAN_ASK_POSTQUARTER_AMOUNT")
	private BigDecimal planAskPostquarterAmount;

	@Column(name="PLAN_ASK_PREQUARTER_AMOUNT")
	private BigDecimal planAskPrequarterAmount;

	@Column(name="PLAN_ASK_QUARTER1_AMOUNT")
	private BigDecimal planAskQuarter1Amount;

	@Column(name="PLAN_ASK_QUARTER2_AMOUNT")
	private BigDecimal planAskQuarter2Amount;

	@Column(name="PLAN_ASK_QUARTER3_AMOUNT")
	private BigDecimal planAskQuarter3Amount;

	@Column(name="PLAN_ASK_QUARTER4_AMOUNT")
	private BigDecimal planAskQuarter4Amount;

	@Column(name="PLANNING_ASK")
	private BigDecimal planningAsk;

	@Column(name="PROJECTED_POSTQUARTER_AMOUNT")
	private BigDecimal projectedPostquarterAmount;

	@Column(name="PROJECTED_PREQUARTER_AMOUNT")
	private BigDecimal projectedPrequarterAmount;

	@Column(name="PROJECTED_QUARTER1_AMOUNT")
	private BigDecimal projectedQuarter1Amount;

	@Column(name="PROJECTED_QUARTER2_AMOUNT")
	private BigDecimal projectedQuarter2Amount;

	@Column(name="PROJECTED_QUARTER3_AMOUNT")
	private BigDecimal projectedQuarter3Amount;

	@Column(name="PROJECTED_QUARTER4_AMOUNT")
	private BigDecimal projectedQuarter4Amount;

	@Column(name="QUARTERLY_DOLLARS_CONFIRMED")
	private BigDecimal quarterlyDollarsConfirmed;

	@Column(name="RATE_CARD_CENTS")
	private BigDecimal rateCardCents;

	@Column(name="RATE_OF_CHANGE")
	private BigDecimal rateOfChange;

	@Column(name="REGISTRATION_TYPE_ID")
	private BigDecimal registrationTypeId;

	@Column(name="SALES_SYSTEM_ID")
	private BigDecimal salesSystemId;

	@Column(name="SEND_TO_SALESFORCE")
	private BigDecimal sendToSalesforce;

	@Column(name="SF_OPPORTUNITY_ID")
	private String sfOpportunityId;

	@Column(name="STATUS_ID")
	private BigDecimal statusId;

	@Column(name="STRATEGY_ID")
	private BigDecimal strategyId;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to Agency
	@ManyToOne
	@JoinColumn(name="AGENCY_ID")
	private Agency agency;

	//bi-directional many-to-one association to BudgetYear
	@ManyToOne
	@JoinColumn(name="BUDGET_YEAR_ID")
	private BudgetYear budgetYear;

	//bi-directional many-to-one association to Deal
	@ManyToOne
	@JoinColumn(name="DEAL_ID")
	private Deal deal;

	//bi-directional many-to-one association to Demographic
	@ManyToOne
	@JoinColumn(name="DEMOGRAPHIC_ID")
	private Demographic demographic;

	//bi-directional many-to-one association to Marketplace
	@ManyToOne
	@JoinColumn(name="MARKETPLACE_ID")
	private Marketplace marketplace;

	//bi-directional many-to-one association to Currency
	@ManyToOne
	@JoinColumn(name="CURRENCY_ID")
	private Currency currency;

	//bi-directional many-to-one association to MeasurementType
	@ManyToOne
	@JoinColumn(name="MEASUREMENT_TYPE_ID")
	private MeasurementType measurementType;

	public Budget() {
	}

	public long getBudgetId() {
		return this.budgetId;
	}

	public void setBudgetId(long budgetId) {
		this.budgetId = budgetId;
	}

	public BigDecimal getActualPostquarterAmount() {
		return this.actualPostquarterAmount;
	}

	public void setActualPostquarterAmount(BigDecimal actualPostquarterAmount) {
		this.actualPostquarterAmount = actualPostquarterAmount;
	}

	public BigDecimal getActualPrequarterAmount() {
		return this.actualPrequarterAmount;
	}

	public void setActualPrequarterAmount(BigDecimal actualPrequarterAmount) {
		this.actualPrequarterAmount = actualPrequarterAmount;
	}

	public BigDecimal getActualQuarter1Amount() {
		return this.actualQuarter1Amount;
	}

	public void setActualQuarter1Amount(BigDecimal actualQuarter1Amount) {
		this.actualQuarter1Amount = actualQuarter1Amount;
	}

	public BigDecimal getActualQuarter2Amount() {
		return this.actualQuarter2Amount;
	}

	public void setActualQuarter2Amount(BigDecimal actualQuarter2Amount) {
		this.actualQuarter2Amount = actualQuarter2Amount;
	}

	public BigDecimal getActualQuarter3Amount() {
		return this.actualQuarter3Amount;
	}

	public void setActualQuarter3Amount(BigDecimal actualQuarter3Amount) {
		this.actualQuarter3Amount = actualQuarter3Amount;
	}

	public BigDecimal getActualQuarter4Amount() {
		return this.actualQuarter4Amount;
	}

	public void setActualQuarter4Amount(BigDecimal actualQuarter4Amount) {
		this.actualQuarter4Amount = actualQuarter4Amount;
	}

	public BigDecimal getAgencyCounterRoc() {
		return this.agencyCounterRoc;
	}

	public void setAgencyCounterRoc(BigDecimal agencyCounterRoc) {
		this.agencyCounterRoc = agencyCounterRoc;
	}

	public BigDecimal getAskPostquarterAmount() {
		return this.askPostquarterAmount;
	}

	public void setAskPostquarterAmount(BigDecimal askPostquarterAmount) {
		this.askPostquarterAmount = askPostquarterAmount;
	}

	public BigDecimal getAskPrequarterAmount() {
		return this.askPrequarterAmount;
	}

	public void setAskPrequarterAmount(BigDecimal askPrequarterAmount) {
		this.askPrequarterAmount = askPrequarterAmount;
	}

	public BigDecimal getAskQuarter1Amount() {
		return this.askQuarter1Amount;
	}

	public void setAskQuarter1Amount(BigDecimal askQuarter1Amount) {
		this.askQuarter1Amount = askQuarter1Amount;
	}

	public BigDecimal getAskQuarter2Amount() {
		return this.askQuarter2Amount;
	}

	public void setAskQuarter2Amount(BigDecimal askQuarter2Amount) {
		this.askQuarter2Amount = askQuarter2Amount;
	}

	public BigDecimal getAskQuarter3Amount() {
		return this.askQuarter3Amount;
	}

	public void setAskQuarter3Amount(BigDecimal askQuarter3Amount) {
		this.askQuarter3Amount = askQuarter3Amount;
	}

	public BigDecimal getAskQuarter4Amount() {
		return this.askQuarter4Amount;
	}

	public void setAskQuarter4Amount(BigDecimal askQuarter4Amount) {
		this.askQuarter4Amount = askQuarter4Amount;
	}

	public BigDecimal getConfidenceLevelId() {
		return this.confidenceLevelId;
	}

	public void setConfidenceLevelId(BigDecimal confidenceLevelId) {
		this.confidenceLevelId = confidenceLevelId;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public BigDecimal getGuaranteedImpressions() {
		return this.guaranteedImpressions;
	}

	public void setGuaranteedImpressions(BigDecimal guaranteedImpressions) {
		this.guaranteedImpressions = guaranteedImpressions;
	}

	public BigDecimal getHhImpressions() {
		return this.hhImpressions;
	}

	public void setHhImpressions(BigDecimal hhImpressions) {
		this.hhImpressions = hhImpressions;
	}

	public BigDecimal getNewBusinessPricing() {
		return this.newBusinessPricing;
	}

	public void setNewBusinessPricing(BigDecimal newBusinessPricing) {
		this.newBusinessPricing = newBusinessPricing;
	}

	public String getNotReturningCustomReason() {
		return this.notReturningCustomReason;
	}

	public void setNotReturningCustomReason(String notReturningCustomReason) {
		this.notReturningCustomReason = notReturningCustomReason;
	}

	public BigDecimal getNotReturningReasonId() {
		return this.notReturningReasonId;
	}

	public void setNotReturningReasonId(BigDecimal notReturningReasonId) {
		this.notReturningReasonId = notReturningReasonId;
	}

	public Date getOptionableDate() {
		return this.optionableDate;
	}

	public void setOptionableDate(Date optionableDate) {
		this.optionableDate = optionableDate;
	}

	public BigDecimal getP1849Impressions() {
		return this.p1849Impressions;
	}

	public void setP1849Impressions(BigDecimal p1849Impressions) {
		this.p1849Impressions = p1849Impressions;
	}

	public BigDecimal getP2554Impressions() {
		return this.p2554Impressions;
	}

	public void setP2554Impressions(BigDecimal p2554Impressions) {
		this.p2554Impressions = p2554Impressions;
	}

	public BigDecimal getP18PlusImpressions() {
		return this.p18PlusImpressions;
	}

	public void setP18PlusImpressions(BigDecimal p18PlusImpressions) {
		this.p18PlusImpressions = p18PlusImpressions;
	}

	public BigDecimal getP2PlusImpressions() {
		return this.p2PlusImpressions;
	}

	public void setP2PlusImpressions(BigDecimal p2PlusImpressions) {
		this.p2PlusImpressions = p2PlusImpressions;
	}

	public BigDecimal getPlanAskPostquarterAmount() {
		return this.planAskPostquarterAmount;
	}

	public void setPlanAskPostquarterAmount(BigDecimal planAskPostquarterAmount) {
		this.planAskPostquarterAmount = planAskPostquarterAmount;
	}

	public BigDecimal getPlanAskPrequarterAmount() {
		return this.planAskPrequarterAmount;
	}

	public void setPlanAskPrequarterAmount(BigDecimal planAskPrequarterAmount) {
		this.planAskPrequarterAmount = planAskPrequarterAmount;
	}

	public BigDecimal getPlanAskQuarter1Amount() {
		return this.planAskQuarter1Amount;
	}

	public void setPlanAskQuarter1Amount(BigDecimal planAskQuarter1Amount) {
		this.planAskQuarter1Amount = planAskQuarter1Amount;
	}

	public BigDecimal getPlanAskQuarter2Amount() {
		return this.planAskQuarter2Amount;
	}

	public void setPlanAskQuarter2Amount(BigDecimal planAskQuarter2Amount) {
		this.planAskQuarter2Amount = planAskQuarter2Amount;
	}

	public BigDecimal getPlanAskQuarter3Amount() {
		return this.planAskQuarter3Amount;
	}

	public void setPlanAskQuarter3Amount(BigDecimal planAskQuarter3Amount) {
		this.planAskQuarter3Amount = planAskQuarter3Amount;
	}

	public BigDecimal getPlanAskQuarter4Amount() {
		return this.planAskQuarter4Amount;
	}

	public void setPlanAskQuarter4Amount(BigDecimal planAskQuarter4Amount) {
		this.planAskQuarter4Amount = planAskQuarter4Amount;
	}

	public BigDecimal getPlanningAsk() {
		return this.planningAsk;
	}

	public void setPlanningAsk(BigDecimal planningAsk) {
		this.planningAsk = planningAsk;
	}

	public BigDecimal getProjectedPostquarterAmount() {
		return this.projectedPostquarterAmount;
	}

	public void setProjectedPostquarterAmount(BigDecimal projectedPostquarterAmount) {
		this.projectedPostquarterAmount = projectedPostquarterAmount;
	}

	public BigDecimal getProjectedPrequarterAmount() {
		return this.projectedPrequarterAmount;
	}

	public void setProjectedPrequarterAmount(BigDecimal projectedPrequarterAmount) {
		this.projectedPrequarterAmount = projectedPrequarterAmount;
	}

	public BigDecimal getProjectedQuarter1Amount() {
		return this.projectedQuarter1Amount;
	}

	public void setProjectedQuarter1Amount(BigDecimal projectedQuarter1Amount) {
		this.projectedQuarter1Amount = projectedQuarter1Amount;
	}

	public BigDecimal getProjectedQuarter2Amount() {
		return this.projectedQuarter2Amount;
	}

	public void setProjectedQuarter2Amount(BigDecimal projectedQuarter2Amount) {
		this.projectedQuarter2Amount = projectedQuarter2Amount;
	}

	public BigDecimal getProjectedQuarter3Amount() {
		return this.projectedQuarter3Amount;
	}

	public void setProjectedQuarter3Amount(BigDecimal projectedQuarter3Amount) {
		this.projectedQuarter3Amount = projectedQuarter3Amount;
	}

	public BigDecimal getProjectedQuarter4Amount() {
		return this.projectedQuarter4Amount;
	}

	public void setProjectedQuarter4Amount(BigDecimal projectedQuarter4Amount) {
		this.projectedQuarter4Amount = projectedQuarter4Amount;
	}

	public BigDecimal getQuarterlyDollarsConfirmed() {
		return this.quarterlyDollarsConfirmed;
	}

	public void setQuarterlyDollarsConfirmed(BigDecimal quarterlyDollarsConfirmed) {
		this.quarterlyDollarsConfirmed = quarterlyDollarsConfirmed;
	}

	public BigDecimal getRateCardCents() {
		return this.rateCardCents;
	}

	public void setRateCardCents(BigDecimal rateCardCents) {
		this.rateCardCents = rateCardCents;
	}

	public BigDecimal getRateOfChange() {
		return this.rateOfChange;
	}

	public void setRateOfChange(BigDecimal rateOfChange) {
		this.rateOfChange = rateOfChange;
	}

	public BigDecimal getRegistrationTypeId() {
		return this.registrationTypeId;
	}

	public void setRegistrationTypeId(BigDecimal registrationTypeId) {
		this.registrationTypeId = registrationTypeId;
	}

	public BigDecimal getSalesSystemId() {
		return this.salesSystemId;
	}

	public void setSalesSystemId(BigDecimal salesSystemId) {
		this.salesSystemId = salesSystemId;
	}

	public BigDecimal getSendToSalesforce() {
		return this.sendToSalesforce;
	}

	public void setSendToSalesforce(BigDecimal sendToSalesforce) {
		this.sendToSalesforce = sendToSalesforce;
	}

	public String getSfOpportunityId() {
		return this.sfOpportunityId;
	}

	public void setSfOpportunityId(String sfOpportunityId) {
		this.sfOpportunityId = sfOpportunityId;
	}

	public BigDecimal getStatusId() {
		return this.statusId;
	}

	public void setStatusId(BigDecimal statusId) {
		this.statusId = statusId;
	}

	public BigDecimal getStrategyId() {
		return this.strategyId;
	}

	public void setStrategyId(BigDecimal strategyId) {
		this.strategyId = strategyId;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public Agency getAgency() {
		return this.agency;
	}

	public void setAgency(Agency agency) {
		this.agency = agency;
	}

	public BudgetYear getBudgetYear() {
		return this.budgetYear;
	}

	public void setBudgetYear(BudgetYear budgetYear) {
		this.budgetYear = budgetYear;
	}

	public Deal getDeal() {
		return this.deal;
	}

	public void setDeal(Deal deal) {
		this.deal = deal;
	}

	public Demographic getDemographic() {
		return this.demographic;
	}

	public void setDemographic(Demographic demographic) {
		this.demographic = demographic;
	}

	public Marketplace getMarketplace() {
		return this.marketplace;
	}

	public void setMarketplace(Marketplace marketplace) {
		this.marketplace = marketplace;
	}

	public Currency getCurrency() {
		return this.currency;
	}

	public MeasurementType getMeasurementType() {
		return this.measurementType;
	}

	// Added to calculate the sum
	public BigDecimal getActualAmount() {
		BigDecimal sum = new BigDecimal(0);
		sum = sum.add(getActualQuarter1Amount());
		sum = sum.add(getActualQuarter2Amount());
		sum = sum.add(getActualQuarter3Amount());
		sum = sum.add(getActualQuarter4Amount());
		sum = sum.add(getActualPrequarterAmount());
		sum = sum.add(getActualPostquarterAmount());
		return sum;
	}

	// Quarterly Mapping
	// 3Q-PY = actual_prequarter_amount
	// 4Q-PY = actual_quarter4_amount
	// 1Q-CY = actual_quarter1_amount
	// 2Q-CY = actual_quarter2_amount
	// 3Q-CY = actual_quarter3_amount
	// 4Q-CY = actual_postquarter_amount
	public QuarterMapping getStartQuarterMapping() {
		QuarterMapping mapping = null;
		if (getActualPrequarterAmount().compareTo(BigDecimal.ZERO) > 0) {
			mapping = new QuarterMapping(3, CyPy.PY);
		} else if (getActualQuarter4Amount().compareTo(BigDecimal.ZERO) > 0) {
			mapping = new QuarterMapping(4, CyPy.PY);
		} else if (getActualQuarter1Amount().compareTo(BigDecimal.ZERO) > 0) {
			mapping = new QuarterMapping(1, CyPy.CY);
		} else if (getActualQuarter2Amount().compareTo(BigDecimal.ZERO) > 0) {
			mapping = new QuarterMapping(2, CyPy.CY);
		} else if (getActualQuarter3Amount().compareTo(BigDecimal.ZERO) > 0) {
			mapping = new QuarterMapping(3, CyPy.CY);
		} else if (getActualPostquarterAmount().compareTo(BigDecimal.ZERO) > 0) {
			mapping = new QuarterMapping(4, CyPy.CY);
		} else {
			mapping = new QuarterMapping(3, CyPy.PY);
		}
		return mapping; 
	}

	public QuarterMapping getEndQuarterMapping() {
		QuarterMapping mapping = null;
		if (getActualPostquarterAmount().compareTo(BigDecimal.ZERO) > 0) {
			mapping = new QuarterMapping(4, CyPy.CY);
		} else if (getActualQuarter3Amount().compareTo(BigDecimal.ZERO) > 0) {
			mapping = new QuarterMapping(3, CyPy.CY);
		} else if (getActualQuarter2Amount().compareTo(BigDecimal.ZERO) > 0) {
			mapping = new QuarterMapping(2, CyPy.CY);
		} else if (getActualQuarter1Amount().compareTo(BigDecimal.ZERO) > 0) {
			mapping = new QuarterMapping(1, CyPy.CY);
		} else if (getActualQuarter4Amount().compareTo(BigDecimal.ZERO) > 0) {
			mapping = new QuarterMapping(4, CyPy.PY);
		} else if (getActualPrequarterAmount().compareTo(BigDecimal.ZERO) > 0) {
			mapping = new QuarterMapping(3, CyPy.PY);
		} else {
			mapping = new QuarterMapping(4, CyPy.CY);
		}
		return mapping; 
	}
	
}