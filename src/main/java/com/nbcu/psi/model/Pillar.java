package com.nbcu.psi.model;


import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import com.nbcu.psi.Tool;

/**
 * The persistent class for the PILLAR database table.
 * 
 */
@Entity
@NamedQuery(name="Pillar.findAll", query="SELECT p FROM Pillar p")
public class Pillar implements Serializable {
	private static final long serialVersionUID = 1L;

	public String toString() {
		return Tool.f("Pillar(%s %s)", getPillarId(), getPillarName());
	}

	@Id
	@Column(name="PILLAR_ID")
	private long pillarId;

	private BigDecimal active;

	@Column(name="CREATED_AT")
	private Timestamp createdAt;

	@Column(name="DISPLAY_ORDER")
	private BigDecimal displayOrder;

	@Column(name="PILLAR_NAME")
	private String pillarName;

	@Column(name="UPDATED_AT")
	private Timestamp updatedAt;

	//bi-directional many-to-one association to Property
	@OneToMany(mappedBy="pillar")
	private List<Property> properties;

	public Pillar() {
	}

	public long getPillarId() {
		return this.pillarId;
	}

	public void setPillarId(long pillarId) {
		this.pillarId = pillarId;
	}

	public BigDecimal getActive() {
		return this.active;
	}

	public void setActive(BigDecimal active) {
		this.active = active;
	}

	public Timestamp getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Timestamp createdAt) {
		this.createdAt = createdAt;
	}

	public BigDecimal getDisplayOrder() {
		return this.displayOrder;
	}

	public void setDisplayOrder(BigDecimal displayOrder) {
		this.displayOrder = displayOrder;
	}

	public String getPillarName() {
		return this.pillarName;
	}

	public void setPillarName(String pillarName) {
		this.pillarName = pillarName;
	}

	public Timestamp getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Timestamp updatedAt) {
		this.updatedAt = updatedAt;
	}

	public List<Property> getProperties() {
		return this.properties;
	}

	public void setProperties(List<Property> properties) {
		this.properties = properties;
	}

	public Property addProperty(Property property) {
		getProperties().add(property);
		property.setPillar(this);

		return property;
	}

	public Property removeProperty(Property property) {
		getProperties().remove(property);
		property.setPillar(null);

		return property;
	}

}