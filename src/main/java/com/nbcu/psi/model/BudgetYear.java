package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.nbcu.psi.Tool;

/**
 * The persistent class for the BUDGET_YEAR database table.
 * 
 */
@Entity
@Table(name="BUDGET_YEAR")
@NamedQuery(name="BudgetYear.findAll", query="SELECT b FROM BudgetYear b")
public class BudgetYear implements Serializable {
	private static final long serialVersionUID = 1L;

	public String toString() {
		return Tool.f("BudgetYear(%s %s)", getBudgetYearId(), getBudgetYearName());
	}

	@Id
	@Column(name="BUDGET_YEAR_ID")
	private long budgetYearId;

	@Column(name="BUDGET_YEAR_NAME")
	private String budgetYearName;

	@Column(name="BUDGETS_CREATED")
	private BigDecimal budgetsCreated;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="CURRENT_PROJECTION_INITIALIZED")
	private BigDecimal currentProjectionInitialized;

	@Column(name="DEFAULT_BUDGET_YEAR")
	private BigDecimal defaultBudgetYear;

	@Column(name="DISPLAY_ORDER")
	private BigDecimal displayOrder;

	@Column(name="FALL_YEAR")
	private BigDecimal fallYear;

	// @Column(name="\"POSITION\"")
	// private BigDecimal position;

	@Column(name="SHOW_IN_DEAL")
	private BigDecimal showInDeal;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to Budget
	@OneToMany(mappedBy="budgetYear")
	private List<Budget> budgets;

	//bi-directional many-to-one association to BudgetYear
	@ManyToOne
	@JoinColumn(name="PRIOR_BUDGET_YEAR_ID")
	private BudgetYear budgetYear;

	//bi-directional many-to-one association to BudgetYear
	@OneToMany(mappedBy="budgetYear")
	private List<BudgetYear> budgetYears;

	public BudgetYear() {
	}

	public long getBudgetYearId() {
		return this.budgetYearId;
	}

	public void setBudgetYearId(long budgetYearId) {
		this.budgetYearId = budgetYearId;
	}

	public String getBudgetYearName() {
		return this.budgetYearName;
	}

	public void setBudgetYearName(String budgetYearName) {
		this.budgetYearName = budgetYearName;
	}

	public BigDecimal getBudgetsCreated() {
		return this.budgetsCreated;
	}

	public void setBudgetsCreated(BigDecimal budgetsCreated) {
		this.budgetsCreated = budgetsCreated;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public BigDecimal getCurrentProjectionInitialized() {
		return this.currentProjectionInitialized;
	}

	public void setCurrentProjectionInitialized(BigDecimal currentProjectionInitialized) {
		this.currentProjectionInitialized = currentProjectionInitialized;
	}

	public BigDecimal getDefaultBudgetYear() {
		return this.defaultBudgetYear;
	}

	public void setDefaultBudgetYear(BigDecimal defaultBudgetYear) {
		this.defaultBudgetYear = defaultBudgetYear;
	}

	public BigDecimal getDisplayOrder() {
		return this.displayOrder;
	}

	public void setDisplayOrder(BigDecimal displayOrder) {
		this.displayOrder = displayOrder;
	}

	public BigDecimal getFallYear() {
		return this.fallYear;
	}

	public void setFallYear(BigDecimal fallYear) {
		this.fallYear = fallYear;
	}

	public Integer getSpringYear() {
		return this.fallYear.intValue() + 1;
	}

	// public BigDecimal getPosition() {
	// 	return this.position;
	// }

	// public void setPosition(BigDecimal position) {
	// 	this.position = position;
	// }

	public BigDecimal getShowInDeal() {
		return this.showInDeal;
	}

	public void setShowInDeal(BigDecimal showInDeal) {
		this.showInDeal = showInDeal;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public List<Budget> getBudgets() {
		return this.budgets;
	}

	public void setBudgets(List<Budget> budgets) {
		this.budgets = budgets;
	}

	public Budget addBudget(Budget budget) {
		getBudgets().add(budget);
		budget.setBudgetYear(this);

		return budget;
	}

	public Budget removeBudget(Budget budget) {
		getBudgets().remove(budget);
		budget.setBudgetYear(null);

		return budget;
	}

	public BudgetYear getBudgetYear() {
		return this.budgetYear;
	}

	public void setBudgetYear(BudgetYear budgetYear) {
		this.budgetYear = budgetYear;
	}

	public List<BudgetYear> getBudgetYears() {
		return this.budgetYears;
	}

	public void setBudgetYears(List<BudgetYear> budgetYears) {
		this.budgetYears = budgetYears;
	}

	public BudgetYear addBudgetYear(BudgetYear budgetYear) {
		getBudgetYears().add(budgetYear);
		budgetYear.setBudgetYear(this);

		return budgetYear;
	}

	public BudgetYear removeBudgetYear(BudgetYear budgetYear) {
		getBudgetYears().remove(budgetYear);
		budgetYear.setBudgetYear(null);

		return budgetYear;
	}

}