package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import com.nbcu.psi.Tool;

@Entity
@Table(name = "SPECIAL_EVENT_TYPE")
@Getter @Setter 
public class SpecialEventType implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name="SPECIAL_EVENT_TYPE_ID")
    private long specialEventTypeId;
    
    @Column(name="SPECIAL_EVENT_TYPE_NAME")
    private String specialEventTypeName;
    
    @Column(name="ACTIVE")
    private boolean active;
    
    @Column(name="DISPLAY_ORDER")
    private int displayOrder;

    @Override
    public String toString() {
        return Tool.f("SpecialEventType(id=%d, name=%s, active=%b)",
            specialEventTypeId, 
            specialEventTypeName,
            active); 
    }
}