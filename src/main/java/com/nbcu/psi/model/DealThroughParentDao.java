package com.nbcu.psi.model;
import com.nbcu.psi.Tool;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import com.nbcu.psi.model.QuarterMapping.CyPy;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter @Setter @NoArgsConstructor
public class DealThroughParentDao {
  public long parentDealId;
  public String parentDealName;
  public long dealLinkId;
  public String dealLinkName;
  public boolean singleOpportunity;
  public long propertyId;
  public long dealId;
  public String dealName;
  public long budgetId;
  public long pbActualPrequarterAmount = 0;
  public long pbActualQuarter4Amount = 0;
  public long pbActualQuarter1Amount = 0;
  public long pbActualQuarter2Amount = 0;
  public long pbActualQuarter3Amount = 0;
  public long pbActualPostquarterAmount = 0;
  public long actualPrequarterAmount = 0;
  public long actualQuarter4Amount = 0;
  public long actualQuarter1Amount = 0;
  public long actualQuarter2Amount = 0;
  public long actualQuarter3Amount = 0;
  public long actualPostquarterAmount = 0;
  public String stage;
  public String sfDealId;
  public String sfOpportunityId;
  public boolean sendToSalesforce;
  public String sfdcVerticalId;
  public String sfdcVerticalName;
  public String sfdcPropertyId;
  public boolean sfSendQuarterly;
  public boolean sfSendPriorYear;
  public boolean sfSendZeroDollars;
  public boolean placeholder;

  public DealThroughParentDao(Object[] o) {
    int idx = 0;
    setParentDealId(Tool.bigDecimalToLong(o[idx++]));
    setParentDealName((String)o[idx++]);
    setDealLinkId(Tool.bigDecimalToLong(o[idx++]));
    setDealLinkName((String)o[idx++]);
    setSingleOpportunity(Tool.bigDecimalToBoolean(o[idx++]));
    setPropertyId(Tool.bigDecimalToLong(o[idx++]));
    setDealId(Tool.bigDecimalToLong(o[idx++]));
    setDealName((String)o[idx++]);
    setBudgetId(Tool.bigDecimalToLong(o[idx++]));
    setPbActualPrequarterAmount(Tool.bigDecimalToLong(o[idx++]));
    setPbActualQuarter4Amount(Tool.bigDecimalToLong(o[idx++]));
    setPbActualQuarter1Amount(Tool.bigDecimalToLong(o[idx++]));
    setPbActualQuarter2Amount(Tool.bigDecimalToLong(o[idx++]));
    setPbActualQuarter3Amount(Tool.bigDecimalToLong(o[idx++]));
    setPbActualPostquarterAmount(Tool.bigDecimalToLong(o[idx++]));
    setActualPrequarterAmount(Tool.bigDecimalToLong(o[idx++]));
    setActualQuarter4Amount(Tool.bigDecimalToLong(o[idx++]));
    setActualQuarter1Amount(Tool.bigDecimalToLong(o[idx++]));
    setActualQuarter2Amount(Tool.bigDecimalToLong(o[idx++]));
    setActualQuarter3Amount(Tool.bigDecimalToLong(o[idx++]));
    setActualPostquarterAmount(Tool.bigDecimalToLong(o[idx++]));
    setStage((String)o[idx++]);
    setSfDealId((String)o[idx++]);
    setSfOpportunityId((String)o[idx++]);
    setSendToSalesforce(Tool.bigDecimalToBoolean(o[idx++]));
    setSfSendQuarterly(Tool.bigDecimalToBoolean(o[idx++]));
    setSfSendPriorYear(Tool.bigDecimalToBoolean(o[idx++]));
    setSfSendZeroDollars(Tool.bigDecimalToBoolean(o[idx++]));
    setSfdcVerticalId((String)o[idx++]);
    setSfdcVerticalName((String)o[idx++]);
    setSfdcPropertyId((String)o[idx++]);
    setPlaceholder(Tool.bigDecimalToBoolean(o[idx++]));
  }

  public long getPrequarterAmount() {
    if (isPlaceholder()) {
      return 0L;
    } else if (isSfSendPriorYear()) {
      return getPbActualPrequarterAmount();
    } else {
      return getActualPrequarterAmount();
    }
  }

  public long getQuarter4Amount() {
    if (isPlaceholder()) {
      return 0L;
    } else if (isSfSendPriorYear()) {
      return getPbActualQuarter4Amount();
    } else {
      return getActualQuarter4Amount();
    }
  }

  public long getQuarter1Amount() {
    if (isPlaceholder()) {
      return 0L;
    } else if (isSfSendPriorYear()) {
      return getPbActualQuarter1Amount();
    } else {
      return getActualQuarter1Amount();
    }
  }

  public long getQuarter2Amount() {
    if (isPlaceholder()) {
      return 0L;
    } else if (isSfSendPriorYear()) {
      return getPbActualQuarter2Amount();
    } else {
      return getActualQuarter2Amount();
    }
  }

  public long getQuarter3Amount() {
    if (isPlaceholder()) {
      return 0L;
    } else if (isSfSendPriorYear()) {
      return getPbActualQuarter3Amount();
    } else {
      return getActualQuarter3Amount();
    }
  }

  public long getPostquarterAmount() {
    if (isPlaceholder()) {
      return 0L;
    } else if (isSfSendPriorYear()) {
      return getPbActualPostquarterAmount();
    } else {
      return getActualPostquarterAmount();
    }
  }

  public String toString() {
    return Tool.f(
        "DealThroughParentDao(parentDealId=%s, parentDealName=%s, dealLinkId=%s, " +
        "dealLinkName=%s, singleOpportunity=%s, propertyId=%s, dealId=%s, dealName=%s, " +
        "budgetId=%s, pbActualPrequarterAmount=%s, pbActualQuarter4Amount=%s, " +
        "pbActualQuarter1Amount=%s, pbActualQuarter2Amount=%s, pbActualQuarter3Amount=%s, " +
        "pbActualPostquarterAmount=%s, actualPrequarterAmount=%s, actualQuarter4Amount=%s, " +
        "actualQuarter1Amount=%s, actualQuarter2Amount=%s, actualQuarter3Amount=%s, " +
        "actualPostquarterAmount=%s, stage=%s, sfDealId=%s, sfOpportunityId=%s, " +
        "sendToSalesforce=%s, sfdcVerticalId=%s, sfdcVerticalName=%s, sfdcPropertyId=%s, " +
        "sfSendQuarterly=%s, sfSendPriorYear=%s, sfSendZeroDollars=%s, placeholder=%s)", 
        parentDealId, parentDealName, dealLinkId, dealLinkName, singleOpportunity, 
        propertyId, dealId, dealName, budgetId, pbActualPrequarterAmount, 
        pbActualQuarter4Amount, pbActualQuarter1Amount, pbActualQuarter2Amount, 
        pbActualQuarter3Amount, pbActualPostquarterAmount, actualPrequarterAmount, 
        actualQuarter4Amount, actualQuarter1Amount, actualQuarter2Amount, 
        actualQuarter3Amount, actualPostquarterAmount, stage, sfDealId, sfOpportunityId, 
        sendToSalesforce, sfdcVerticalId, sfdcVerticalName, sfdcPropertyId, 
        sfSendQuarterly, sfSendPriorYear, sfSendZeroDollars, placeholder);
  }

  /**
   * If any budget has sf_deal_id set, then assume the whole deal link has been sent.
   */
  public boolean isAlreadySent() {
    return Tool.isStrBlank(getSfDealId());
  }

  //may need to use deal.java to calculate quarter date

}