package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.util.Date;


/**
 * The persistent class for the ROLE_ENTITLEMENT database table.
 * 
 */
@Entity
@Table(name="ROLE_ENTITLEMENT")
@NamedQuery(name="RoleEntitlement.findAll", query="SELECT r FROM RoleEntitlement r")
public class RoleEntitlement implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name="ROLE_ENTITLEMENT_ID")
	private long roleEntitlementId;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to AppRole
	@ManyToOne
	@JoinColumn(name="APP_ROLE_ID")
	private AppRole appRole;

	//bi-directional many-to-one association to AppUser
	@ManyToOne
	@JoinColumn(name="LAST_UPDATED_BY_ID")
	private AppUser appUser;

	//bi-directional many-to-one association to Entitlement
	@ManyToOne
	@JoinColumn(name="ENTITLEMENT_ID")
	private Entitlement entitlement;

	public RoleEntitlement() {
	}

	public long getRoleEntitlementId() {
		return this.roleEntitlementId;
	}

	public void setRoleEntitlementId(long roleEntitlementId) {
		this.roleEntitlementId = roleEntitlementId;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public AppRole getAppRole() {
		return this.appRole;
	}

	public void setAppRole(AppRole appRole) {
		this.appRole = appRole;
	}

	public AppUser getAppUser() {
		return this.appUser;
	}

	public void setAppUser(AppUser appUser) {
		this.appUser = appUser;
	}

	public Entitlement getEntitlement() {
		return this.entitlement;
	}

	public void setEntitlement(Entitlement entitlement) {
		this.entitlement = entitlement;
	}

}