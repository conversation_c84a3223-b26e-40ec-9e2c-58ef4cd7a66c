package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.nbcu.psi.Tool;

/**
 * The persistent class for the VERTICAL database table.
 */
@Entity
@NamedQuery(name="Vertical.findAll", query="SELECT v FROM Vertical v")
public class Vertical implements Serializable {
    private static final long serialVersionUID = 1L;

    public String toString() {
        return Tool.f("Vertical(%s %s)", getVerticalId(), getVerticalName());
    }

    @Id
    @Column(name="VERTICAL_ID")
    private long verticalId;

    @Column(name="VERTICAL_NAME")
    private String verticalName;

    @Temporal(TemporalType.DATE)
    @Column(name="CREATED_AT")
    private Date createdAt;

    @Temporal(TemporalType.DATE)
    @Column(name="UPDATED_AT")
    private Date updatedAt;

    @Column(name="DISPLAY_ORDER")
    private BigDecimal displayOrder;

    @Column(name="LAST_UPDATED_BY_ID")
    private BigDecimal lastUpdatedById;

    @Column(name="ACTIVE")
    private BigDecimal active;

    //bi-directional many-to-one association to Property
    @OneToMany(mappedBy="vertical")
    private List<Property> properties;

    public Vertical() {
    }

    public long getVerticalId() {
        return this.verticalId;
    }

    public void setVerticalId(long verticalId) {
        this.verticalId = verticalId;
    }

    public String getVerticalName() {
        return this.verticalName;
    }

    public void setVerticalName(String verticalName) {
        this.verticalName = verticalName;
    }

    public Date getCreatedAt() {
        return this.createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public BigDecimal getDisplayOrder() {
        return this.displayOrder;
    }

    public void setDisplayOrder(BigDecimal displayOrder) {
        this.displayOrder = displayOrder;
    }

    public BigDecimal getLastUpdatedById() {
        return this.lastUpdatedById;
    }

    public void setLastUpdatedById(BigDecimal lastUpdatedById) {
        this.lastUpdatedById = lastUpdatedById;
    }

    public BigDecimal getActive() {
        return this.active;
    }

    public void setActive(BigDecimal active) {
        this.active = active;
    }

    public List<Property> getProperties() {
        return this.properties;
    }

    public void setProperties(List<Property> properties) {
        this.properties = properties;
    }

    public Property addProperty(Property property) {
        getProperties().add(property);
        property.setVertical(this);
        return property;
    }

    public Property removeProperty(Property property) {
        getProperties().remove(property);
        property.setVertical(null);
        return property;
    }
} 