package com.nbcu.psi.model;
import com.nbcu.psi.Tool;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import com.nbcu.psi.model.QuarterMapping.CyPy;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter @Setter @NoArgsConstructor
public class ParentDealRecord {
  public long parentDealId;
  public String parentDealName;
  public long agencyId;
  public long advertiserId;
  public long marketplaceId;
  public long parentDealTypeId;
  public long budgetId;
  public boolean placeholder;
  List<DealLinkRecord> dealLinks = new ArrayList<DealLinkRecord>();
  List<DealRecord> deals = new ArrayList<DealRecord>();

  public ParentDealRecord(DealThroughParentDao dao) {
    parentDealId = dao.getParentDealId();
    parentDealName = dao.getParentDealName();
    placeholder = dao.isPlaceholder();
  }

  // deals and dealLinks are both empty
  public boolean isEmpty() {
    return deals.isEmpty() && dealLinks.isEmpty();
  }

  /**
   * The dollar in sf deal only has value when it is a placeholder deal.
   */
  public long getPlaceholderDollar() {
    if (placeholder) {
      return deals.get(0).getActualAmount();
    } else {
      return 0;
    }
  }

  public int getFirstBudgetId() {
    if (deals.size() > 0) {
      return (int)(deals.get(0).getBudgetId());
    } else if (dealLinks.size() > 0) {
      return (int)(dealLinks.get(0).getBudgetId());
    } else {
      return 0;
    }
  }

  public String toString() {
    StringBuffer sb = new StringBuffer();
    sb.append(Tool.f("ParentDealRecord(%s, '%s')\n", 
                  getParentDealId(),
                  getParentDealName()));
    for (DealLinkRecord dealLink: dealLinks) {
      sb.append(Tool.f("\t%s\n", dealLink));
    }
    for (DealRecord deal: deals) {
      sb.append(Tool.f("\t%s\n", deal));
    }
    return sb.toString();
  }

  public void addDeal(DealRecord deal) {
    deals.add(deal);
  }

  public void aggregateDeals() {
    for (DealLinkRecord dealLink: dealLinks) {
      dealLink.aggregateDeals();
    }
  }

  public DealLinkRecord findOrCreateDealLinkRecord(DealThroughParentDao dao) {
    long dealLinkId = dao.getDealLinkId();
    for (DealLinkRecord record: dealLinks) {
      if (record.getDealLinkId() == dealLinkId) {
        return record;
      }
    }
    DealLinkRecord record = new DealLinkRecord(dao);
    dealLinks.add(record);
    return record;
  }

}