package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * The persistent class for the RATING_STREAM database table.
 * 
 */
@Entity
@Table(name="RATING_STREAM")
@NamedQuery(name="RatingStream.findAll", query="SELECT r FROM RatingStream r")
public class RatingStream implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name="RATING_STREAM_ID")
	private long ratingStreamId;

	private BigDecimal active;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="DEFAULT_RATING_STREAM")
	private BigDecimal defaultRatingStream;

	@Column(name="DISPLAY_ORDER")
	private BigDecimal displayOrder;

	@Column(name="RATING_STREAM_NAME")
	private String ratingStreamName;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to Deal
	@OneToMany(mappedBy="ratingStream")
	private List<Deal> deals;

	//bi-directional many-to-one association to AppUser
	@ManyToOne
	@JoinColumn(name="LAST_UPDATED_BY_ID")
	private AppUser appUser;

	public RatingStream() {
	}

	public long getRatingStreamId() {
		return this.ratingStreamId;
	}

	public void setRatingStreamId(long ratingStreamId) {
		this.ratingStreamId = ratingStreamId;
	}

	public BigDecimal getActive() {
		return this.active;
	}

	public void setActive(BigDecimal active) {
		this.active = active;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public BigDecimal getDefaultRatingStream() {
		return this.defaultRatingStream;
	}

	public void setDefaultRatingStream(BigDecimal defaultRatingStream) {
		this.defaultRatingStream = defaultRatingStream;
	}

	public BigDecimal getDisplayOrder() {
		return this.displayOrder;
	}

	public void setDisplayOrder(BigDecimal displayOrder) {
		this.displayOrder = displayOrder;
	}

	public String getRatingStreamName() {
		return this.ratingStreamName;
	}

	public void setRatingStreamName(String ratingStreamName) {
		this.ratingStreamName = ratingStreamName;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public List<Deal> getDeals() {
		return this.deals;
	}

	public void setDeals(List<Deal> deals) {
		this.deals = deals;
	}

	public Deal addDeal(Deal deal) {
		getDeals().add(deal);
		deal.setRatingStream(this);

		return deal;
	}

	public Deal removeDeal(Deal deal) {
		getDeals().remove(deal);
		deal.setRatingStream(null);

		return deal;
	}

	public AppUser getAppUser() {
		return this.appUser;
	}

	public void setAppUser(AppUser appUser) {
		this.appUser = appUser;
	}

}