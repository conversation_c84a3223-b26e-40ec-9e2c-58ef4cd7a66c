package com.nbcu.psi.model;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import com.nbcu.psi.Tool;

@Entity
@Table(name = "SPECIAL_EVENT")
@Getter @Setter
public class SpecialEvent implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name="SPECIAL_EVENT_ID")
    private long specialEventId;

    @Column(name="SPECIAL_EVENT_NAME")
    private String specialEventName;

    @Column(name="SPECIAL_EVENT_TYPE_ID")
    private long specialEventTypeId;

    @Column(name="BUDGET_YEAR_ID")
    private long budgetYearId;

    @Column(name="PRIOR_SPECIAL_EVENT_ID")
    private Long priorSpecialEventId;

    @Column(name="ACTIVE")
    private boolean active;

    @Column(name="DISPLAY_ORDER")
    private int displayOrder;

    @Column(name="QUARTER_ID")
    private long quarterId;

    @Column(name="START_DATE")
    private Date startDate;

    @Column(name="END_DATE")
    private Date endDate;

    @Column(name="QUARTER_DISTRIBUTION")
    private String quarterDistribution;

    @Column(name="DEFAULT_MEASUREMENT_TYPE_ID")
    private long defaultMeasurementTypeId;

    @Column(name="DEFAULT_CURRENCY_ID")
    private long defaultCurrencyId;

    @Column(name="DEFAULT_DEMOGRAPHIC_ID")
    private long defaultDemographicId;

    @Column(name="DEFAULT_RATING_STREAM_ID")
    private long defaultRatingStreamId;

    @Column(name="CREATED_AT")
    private Date createdAt;

    @Column(name="UPDATED_AT")
    private Date updatedAt;

    @Column(name="LAST_UPDATED_BY_ID")
    private Long lastUpdatedById;

    @ManyToOne
    @JoinColumn(name="SPECIAL_EVENT_TYPE_ID", insertable=false, updatable=false)
    private SpecialEventType specialEventType;

    @Override
    public String toString() {
        return Tool.f("SpecialEvent(id=%d, name=%s, type=%d, year=%d, active=%b)",
            specialEventId,
            specialEventName,
            specialEventTypeId,
            budgetYearId,
            active);
    }
}