package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.nbcu.psi.Tool;

/**
 * The persistent class for the SELLING_VERTICAL database table.
 * 
 */
@Entity
@Table(name="SELLING_VERTICAL")
@NamedQuery(name="SellingVertical.findAll", query="SELECT s FROM SellingVertical s")
public class SellingVertical implements Serializable {
	private static final long serialVersionUID = 1L;

	public String toString() {
		return Tool.f("SellingVertical(%s %s)", getSellingVerticalId(), getSellingVerticalName());
	}

	@Id
	@Column(name="SELLING_VERTICAL_ID")
	private long sellingVerticalId;

	private BigDecimal active;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="DISPLAY_ORDER")
	private BigDecimal displayOrder;

	private String email;

	@Column(name="SELLING_VERTICAL_NAME")
	private String sellingVerticalName;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to Property
	@OneToMany(mappedBy="sellingVertical", fetch = FetchType.EAGER)
	private List<Property> properties;

	public SellingVertical() {
	}

	public long getSellingVerticalId() {
		return this.sellingVerticalId;
	}

	public void setSellingVerticalId(long sellingVerticalId) {
		this.sellingVerticalId = sellingVerticalId;
	}

	public BigDecimal getActive() {
		return this.active;
	}

	public void setActive(BigDecimal active) {
		this.active = active;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public BigDecimal getDisplayOrder() {
		return this.displayOrder;
	}

	public void setDisplayOrder(BigDecimal displayOrder) {
		this.displayOrder = displayOrder;
	}

	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getSellingVerticalName() {
		return this.sellingVerticalName;
	}

	public void setSellingVerticalName(String sellingVerticalName) {
		this.sellingVerticalName = sellingVerticalName;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public List<Property> getProperties() {
		return this.properties;
	}

	public void setProperties(List<Property> properties) {
		this.properties = properties;
	}

	public Property addProperty(Property property) {
		getProperties().add(property);
		property.setSellingVertical(this);

		return property;
	}

	public Property removeProperty(Property property) {
		getProperties().remove(property);
		property.setSellingVertical(null);

		return property;
	}

}