package com.nbcu.psi.model;


import java.io.Serializable;
import javax.persistence.*;

import com.nbcu.psi.Tool;

import java.math.BigDecimal;
import java.util.Date;


/**
 * The persistent class for the SYSTEM_KEY_ASC database table.
 * 
 */
@Entity
@Table(name="SYSTEM_KEY_ASC")
@NamedQuery(name="SystemKeyAsc.findAll", query="SELECT s FROM SystemKeyAsc s")
public class SystemKeyAsc implements Serializable {
	private static final long serialVersionUID = 1L;

	public String toString() {
		return Tool.f("SystemKeyAsc(%s %s)", getPamKey(), getExternalSystemKey());
	}

  @Id
  @Column(name="SYSTEM_KEY_ASC_ID")
	private BigDecimal systemKeyAscId;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="EXTERNAL_KEY_TYPE_ID")
	private BigDecimal externalKeyTypeId;

	@Column(name="EXTERNAL_SYSTEM_ID")
	private BigDecimal externalSystemId;

	@Column(name="EXTERNAL_SYSTEM_KEY")
	private String externalSystemKey;

	@Column(name="EXTERNAL_SYSTEM_KEY_NBR")
	private BigDecimal externalSystemKeyNbr;

	@Column(name="PAM_KEY")
	private String pamKey;

	@Column(name="PAM_KEY_NBR")
	private BigDecimal pamKeyNbr;

	@Column(name="SYSTEM_KEY_ASC_TYPE_ID")
	private BigDecimal systemKeyAscTypeId;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	public SystemKeyAsc() {
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public BigDecimal getExternalKeyTypeId() {
		return this.externalKeyTypeId;
	}

	public void setExternalKeyTypeId(BigDecimal externalKeyTypeId) {
		this.externalKeyTypeId = externalKeyTypeId;
	}

	public BigDecimal getExternalSystemId() {
		return this.externalSystemId;
	}

	public void setExternalSystemId(BigDecimal externalSystemId) {
		this.externalSystemId = externalSystemId;
	}

	public String getExternalSystemKey() {
		return this.externalSystemKey;
	}

	public void setExternalSystemKey(String externalSystemKey) {
		this.externalSystemKey = externalSystemKey;
	}

	public BigDecimal getExternalSystemKeyNbr() {
		return this.externalSystemKeyNbr;
	}

	public void setExternalSystemKeyNbr(BigDecimal externalSystemKeyNbr) {
		this.externalSystemKeyNbr = externalSystemKeyNbr;
	}

	public String getPamKey() {
		return this.pamKey;
	}

	public void setPamKey(String pamKey) {
		this.pamKey = pamKey;
	}

	public BigDecimal getPamKeyNbr() {
		return this.pamKeyNbr;
	}

	public void setPamKeyNbr(BigDecimal pamKeyNbr) {
		this.pamKeyNbr = pamKeyNbr;
	}

	public BigDecimal getSystemKeyAscId() {
		return this.systemKeyAscId;
	}

	public void setSystemKeyAscId(BigDecimal systemKeyAscId) {
		this.systemKeyAscId = systemKeyAscId;
	}

	public BigDecimal getSystemKeyAscTypeId() {
		return this.systemKeyAscTypeId;
	}

	public void setSystemKeyAscTypeId(BigDecimal systemKeyAscTypeId) {
		this.systemKeyAscTypeId = systemKeyAscTypeId;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

}