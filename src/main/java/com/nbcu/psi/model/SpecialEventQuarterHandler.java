package com.nbcu.psi.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.NonNull;
import org.springframework.stereotype.Component;
import java.math.BigDecimal;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.Optional;
import com.nbcu.psi.service.BudgetYearService;
import com.nbcu.psi.service.QuarterService;

@Component
@RequiredArgsConstructor
public class SpecialEventQuarterHandler {
    @NonNull
    private final BudgetYearService budgetYearService;
    @NonNull
    private final QuarterService quarterService;
    @Getter
    private String[] quarterNames = new String[6];
    private String yearSeason;
    private String py;
    private String cy;
    private String dealYear;
    private String season;

    /**
     * Aligns percentage distribution across quarters based on a text distribution format.
     * 
     * @param budgetYearName Format: "2025/2026" - the budget year period
     * @param quarterDistribution Format: "Q2-26:40%, Q4-26:60%" - quarter distribution with percentages
     * @return double array with 6 elements representing distribution across quarters [3QPY, 4QPY, 1QCY, 2QCY, 3QCY, 4QCY]
     */
    public double[] alignPercentage(String budgetYearName, String quarterDistribution) {
        // Extract the year components
        String py = budgetYearName.substring(2, 4);  // Previous year - "25"
        String cy = budgetYearName.substring(7, 9);  // Current year - "26"
        
        // Initialize result array with zeros
        double[] result = new double[6];
        
        // Parse the quarter distribution string with regex
        Pattern pattern = Pattern.compile("Q([1-4])-([0-9]{2}):([0-9.]+)%");
        Matcher matcher = pattern.matcher(quarterDistribution);
        
        while (matcher.find()) {
            int quarterNum = Integer.parseInt(matcher.group(1));
            String year = matcher.group(2);
            double percentage = Double.parseDouble(matcher.group(3)) / 100.0;
            
            // Map to the correct index in our result array
            int index = -1;
            
            // Quarter 1-4 of current year maps to indices 2-5
            if (year.equals(cy)) {
                index = quarterNum + 1; // Q1->2, Q2->3, Q3->4, Q4->5
            }
            // Quarter 3-4 of previous year maps to indices 0-1
            else if (year.equals(py) && (quarterNum == 3 || quarterNum == 4)) {
                index = quarterNum - 3; // Q3->0, Q4->1
            }
            
            if (index >= 0 && index < 6) {
                result[index] = percentage;
            }
        }
        
        return result;
    }

    /**
     * Initializes quarter names and season information based on the special event's budget year.
     * Follows same logic as Global.setCurrentBudgetYear()
     */
    public void initializeForEvent(SpecialEvent event) throws Exception {
        Optional<BudgetYear> budgetYearOpt = budgetYearService.findById(event.getBudgetYearId());
        if (!budgetYearOpt.isPresent()) {
            throw new IllegalStateException("Budget year not found: " + event.getBudgetYearId());
        }
        BudgetYear budgetYear = budgetYearOpt.get();
        String budgetYearName = budgetYear.getBudgetYearName();
        // Parse year information from budget year name (e.g., "2023/2024")
        this.dealYear = budgetYearName.substring(5, 9);    // e.g., "2024"
        this.py = budgetYearName.substring(2, 4);          // e.g., "23"
        this.cy = budgetYearName.substring(7, 9);          // e.g., "24"
        this.season = py + "/" + cy;                       // e.g., "23/24"
        // Initialize quarter names array
        this.quarterNames[0] = "3Q" + py;  // 3Q23
        this.quarterNames[1] = "4Q" + py;  // 4Q23
        this.quarterNames[2] = "1Q" + cy;  // 1Q24
        this.quarterNames[3] = "2Q" + cy;  // 2Q24
        this.quarterNames[4] = "3Q" + cy;  // 3Q24
        this.quarterNames[5] = "4Q" + cy;  // 4Q24
        this.yearSeason = dealYear;        // "2024"
    }

    /**
     * Populates Salesforce quarter amount fields based on the special event's quarter
     * and budget year. Also adds season/year information to the value map.
     * 
     * Modified to support quarter distribution if present.
     */
    public void populateQuarterAmount(SpecialEventDealDetail detail, Map<String, Object> vMap) throws Exception {
        SpecialEvent event = detail.getSpecialEvent();
        BudgetYear budgetYear = budgetYearService.findById(event.getBudgetYearId())
            .orElseThrow(() -> new IllegalStateException("Budget year not found: " + event.getBudgetYearId()));
        initializeForEvent(event);
        
        // Initialize quarter amounts to zero
        vMap.put("amount_pre", 0);
        vMap.put("amount_q4", 0);
        vMap.put("amount_q1", 0);
        vMap.put("amount_q2", 0);
        vMap.put("amount_q3", 0);
        vMap.put("amount_post", 0);
        
        // Get the budget amount
        BigDecimal amount = detail.getBudgetDollars();
        
        // Check if quarter distribution is specified
        String quarterDistribution = event.getQuarterDistribution();
        if (quarterDistribution != null && !quarterDistribution.trim().isEmpty()) {
            // Use alignPercentage to distribute the amount
            String budgetYearName = budgetYear.getBudgetYearName();
            double[] percentages = alignPercentage(budgetYearName, quarterDistribution);
            
            // Apply the percentages to the budget amount
            String[] amountFields = {"amount_pre", "amount_q4", "amount_q1", "amount_q2", "amount_q3", "amount_post"};
            for (int i = 0; i < amountFields.length; i++) {
                if (percentages[i] > 0) {
                    BigDecimal quarterAmount = amount.multiply(BigDecimal.valueOf(percentages[i]));
                    vMap.put(amountFields[i], quarterAmount);
                }
            }
        } else {
            // Use the original logic to determine quarter based on event quarter
            Quarter quarterEntity = quarterService.findById(event.getQuarterId())
                .orElseThrow(() -> new IllegalStateException("Quarter not found: " + event.getQuarterId()));
            int quarter = quarterEntity.getQuarter().intValue();
            int year = quarterEntity.getYear().intValue();
            
            // Map the quarter's amount to the appropriate Salesforce field
            String amountField = mapQuarterToAmountField(quarter, year, budgetYear);
            if (amountField != null) {
                vMap.put(amountField, amount);
            }
        }
        
        // Set the total amount
        vMap.put("amount", amount);
        
        // Add season information
        vMap.put("season", season);
        vMap.put("year_season", yearSeason);
        vMap.put("deal_year", dealYear);
    }

    /**
     * Maps a quarter number (1-4) to the appropriate Salesforce amount field based on
     * whether it falls in the fall or spring year of the budget year.
     */
    public String mapQuarterToAmountField(int quarter, int year, BudgetYear budgetYear) {
      int fallYear = budgetYear.getFallYear().intValue();
      int springYear = fallYear + 1;
      // Debug logging
      System.out.println("\nDebug Quarter Mapping:");
      System.out.println("Input quarter: " + quarter);
      System.out.println("Input year: " + year);
      System.out.println("Fall year: " + fallYear);
      System.out.println("Spring year: " + springYear);
      // Quarter must be 1-4
      if (quarter >= 1 && quarter <= 4) {
          String result = null;
          switch(quarter) {
              case 3: // Q3
                  if (year == fallYear) {
                      result = "amount_pre";  // Q3 in fall year
                      System.out.println("Q3 fall year -> amount_pre");
                  } else {
                      result = "amount_q3";   // Q3 in spring year
                      System.out.println("Q3 spring year -> amount_q3");
                  }
                  break;
              case 4: // Q4
                  if (year == fallYear) {
                      result = "amount_q4";   // Q4 in fall year
                      System.out.println("Q4 fall year -> amount_q4");
                  } else {
                      result = "amount_post"; // Q4 in spring year
                      System.out.println("Q4 spring year -> amount_post");
                  }
                  break;
              case 1: // Q1
                  if (year == springYear) {
                      result = "amount_q1";   // Q1 in spring year
                      System.out.println("Q1 spring year -> amount_q1");
                  }
                  break;
              case 2: // Q2
                  if (year == springYear) {
                      result = "amount_q2";   // Q2 in spring year
                      System.out.println("Q2 spring year -> amount_q2");
                  }
                  break;
          }
          System.out.println("Returning amount field: " + result);
          return result;
      }
      System.out.println("Quarter out of range 1-4, returning null");
      return null; // Quarter doesn't map to any amount field
    }

    public String getQuarterName(int index) {
        if (index >= 0 && index < quarterNames.length) {
            return quarterNames[index];
        }
        return null;
    }

    public String getSeason() {
        return season;
    }

    public String getDealYear() {
        return dealYear;
    }

    public String getYearSeason() {
        return yearSeason;
    }
}