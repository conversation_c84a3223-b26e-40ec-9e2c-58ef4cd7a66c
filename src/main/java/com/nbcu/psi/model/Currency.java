package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.nbcu.psi.Tool;

/**
 * The persistent class for the CURRENCY database table.
 * 
 */
@Entity
@NamedQuery(name="Currency.findAll", query="SELECT c FROM Currency c")
public class Currency implements Serializable {
	private static final long serialVersionUID = 1L;

  public String toString() {
		return Tool.f("Currency(%s %s)", getCurrencyId(), getCurrencyName());
	}

	@Id
	@Column(name="CURRENCY_ID")
	private long currencyId;

	private BigDecimal active;

	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="CURRENCY_NAME")
	private String currencyName;

	@Column(name="DISPLAY_ORDER")
	private BigDecimal displayOrder;

	@Column(name="UPDATED_AT")
	private Date updatedAt;

	//bi-directional many-to-one association to Budget
	@OneToMany(mappedBy="currency")
	private List<Budget> budgets;

	//bi-directional many-to-one association to Deal
	@OneToMany(mappedBy="currency")
	private List<Deal> deals;

	public Currency() {
	}

	public long getCurrencyId() {
		return this.currencyId;
	}

	public BigDecimal getActive() {
		return this.active;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public String getCurrencyName() {
		return this.currencyName;
	}

	public BigDecimal getDisplayOrder() {
		return this.displayOrder;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public List<Budget> getBudgets() {
		return this.budgets;
	}

	public List<Deal> getDeals() {
		return this.deals;
	}
}