package com.nbcu.psi.model;

import java.io.Serializable;
import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * The persistent class for the "QUARTER" database table.
 * 
 */
@Entity
@Table(name="QUARTER")
@NamedQuery(name="Quarter.findAll", query="SELECT q FROM Quarter q")
public class Quarter implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@Column(name="QUARTER_ID")
	private long quarterId;

	@Column(name="CALENDAR_YEAR_ID")
	private BigDecimal calendarYearId;

	@Temporal(TemporalType.DATE)
	@Column(name="CREATED_AT")
	private Date createdAt;

	@Column(name="DISPLAY_ORDER")
	private BigDecimal displayOrder;

	@Column(name="LAST_UPDATED_BY_ID")
	private BigDecimal lastUpdatedById;

	@Column(name="LOCK_STATUS")
	private BigDecimal lockStatus;

	@Column(name="\"QUARTER\"")
	private BigDecimal quarter;

	@Column(name="QUARTER_NAME")
	private String quarterName;

	@Column(name="SEASON_ID")
	private BigDecimal seasonId;

	@Temporal(TemporalType.DATE)
	@Column(name="UPDATED_AT")
	private Date updatedAt;

	@Column(name="\"YEAR\"")
	private BigDecimal year;

	//bi-directional many-to-one association to FinanceQuarter
	@OneToMany(mappedBy="quarter", fetch=FetchType.EAGER)
	private List<FinanceQuarter> financeQuarters;

	public Quarter() {
	}

	public long getQuarterId() {
		return this.quarterId;
	}

	public void setQuarterId(long quarterId) {
		this.quarterId = quarterId;
	}

	public BigDecimal getCalendarYearId() {
		return this.calendarYearId;
	}

	public void setCalendarYearId(BigDecimal calendarYearId) {
		this.calendarYearId = calendarYearId;
	}

	public Date getCreatedAt() {
		return this.createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public BigDecimal getDisplayOrder() {
		return this.displayOrder;
	}

	public void setDisplayOrder(BigDecimal displayOrder) {
		this.displayOrder = displayOrder;
	}

	public BigDecimal getLastUpdatedById() {
		return this.lastUpdatedById;
	}

	public void setLastUpdatedById(BigDecimal lastUpdatedById) {
		this.lastUpdatedById = lastUpdatedById;
	}

	public BigDecimal getLockStatus() {
		return this.lockStatus;
	}

	public void setLockStatus(BigDecimal lockStatus) {
		this.lockStatus = lockStatus;
	}

	public BigDecimal getQuarter() {
		return this.quarter;
	}

	public void setQuarter(BigDecimal quarter) {
		this.quarter = quarter;
	}

	public String getQuarterName() {
		return this.quarterName;
	}

	public void setQuarterName(String quarterName) {
		this.quarterName = quarterName;
	}

	public BigDecimal getSeasonId() {
		return this.seasonId;
	}

	public void setSeasonId(BigDecimal seasonId) {
		this.seasonId = seasonId;
	}

	public Date getUpdatedAt() {
		return this.updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	public BigDecimal getYear() {
		return this.year;
	}

	public void setYear(BigDecimal year) {
		this.year = year;
	}

	public List<FinanceQuarter> getFinanceQuarters() {
		return this.financeQuarters;
	}

	public void setFinanceQuarters(List<FinanceQuarter> financeQuarters) {
		this.financeQuarters = financeQuarters;
	}

	public FinanceQuarter addFinanceQuarter(FinanceQuarter financeQuarter) {
		getFinanceQuarters().add(financeQuarter);
		financeQuarter.setQuarter(this);

		return financeQuarter;
	}

	public FinanceQuarter removeFinanceQuarter(FinanceQuarter financeQuarter) {
		getFinanceQuarters().remove(financeQuarter);
		financeQuarter.setQuarter(null);

		return financeQuarter;
	}

}
