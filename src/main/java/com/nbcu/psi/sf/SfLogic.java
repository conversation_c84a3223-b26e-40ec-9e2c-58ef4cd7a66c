package com.nbcu.psi.sf;

import com.nbcu.psi.model.DealLinkRecord;
import com.nbcu.psi.model.DealRecord;
import com.nbcu.psi.model.ParentDealRecord;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import com.nbcu.psi.Global;
import com.nbcu.psi.Tool;

/**
 * This class is used to send higher level requests to Salesforce.
 * Will use SfClient to send low level requests.
 */
@Service
@RequiredArgsConstructor
public class SfLogic {

  @NonNull
  private Global g;

  @NonNull
  private Environment env;

  @NonNull
  private SfClient sfClient;

  @NonNull
  private SfJsonBuilder sfJsonBuilder;

  /**
   * Send a parent deal to Salesforce and return the sf_deal_id.
   * @throws Exception
   */
  public String sendParentDeal(ParentDealRecord parent) throws Exception {
    String requestJson = sfJsonBuilder.buildDealJson(parent);
    // Tool.pr("payload:\n" + requestJson);
    String responseJson = sfClient.sendComposite(requestJson);
    String sfDealId = sfClient.extractObjectId(responseJson, "Deal__c");
    return sfDealId;
  }

  /**
   * Send a single deal (registration) to Salesforce and return the sf_opportunity_id.
   * @deal: the deal to be sent
   * @sfDealId: the sf_deal_id of the parent deal
   * @throws Exception
   */
  public String sendDeal(DealRecord deal, String sfDealId) throws Exception {
    // sfJsonBuilder.vMap.put("sf_deal_id", sfDealId);
    String oppJson = sfJsonBuilder.buildOpportunityDealJson(deal);
    // Tool.pr("oppJson:\n" + oppJson);
    String ResponseJson = sfClient.sendComposite(oppJson);
    String sfOppId = sfClient.extractObjectId(ResponseJson, "Opportunity");
    return sfOppId;
  }

  /**
   * Send a single deal (registration) to Salesforce and return the sf_opportunity_id.
   * @deal: the deal to be sent
   * @sfDealId: the sf_deal_id of the parent deal
   * @throws Exception
   */
  public String syncDeal(DealRecord deal, String sfOppId) throws Exception {
    // sfJsonBuilder.vMap.put("sf_deal_id", sfDealId);
    String oppJson = sfJsonBuilder.buildSyncOpportunityDealJson(deal);
    // Tool.pr("oppJson:\n" + oppJson);
    String ResponseJson = sfClient.sendComposite(oppJson);
    // String sfOppId = sfClient.extractObjectId(ResponseJson, "Opportunity");
    return sfOppId;
  }  

  /**
   * Send a single deal (registration) to Salesforce and return the sf_opportunity_id.
   * @deal: the deal to be sent
   * @sfDealId: the sf_deal_id of the parent deal
   * @throws Exception
   */
  public String syncDealQuarterly(DealRecord deal) throws Exception {
    // sfJsonBuilder.vMap.put("sf_deal_id", sfDealId);
    String oppJson = sfJsonBuilder.buildSyncQuarterlyOpportunityDealJson(deal);
    Tool.pr("oppJson:\n" + oppJson);
    
    String ResponseJson = sfClient.sendComposite(oppJson);
    // get opportunity_deal logic should be
    List<String> sfOppIds = sfClient.extractObjectIds(ResponseJson, "Opportunity");

    return buildSyncConcatenatedIds(sfOppIds);
  }  
  /**
   * Send a single deal (registration) to Salesforce as multiple opportunities 
   * and return multiple sf_opportunity_ids.
   * @deal: the deal to be sent
   * @sfDealId: the sf_deal_id of the parent deal
   * @throws Exception
   */
  public String sendDealQuarterly(DealRecord deal, String sfDealId) throws Exception {
    // sfJsonBuilder.vMap.put("sf_deal_id", sfDealId);
    String oppJson = sfJsonBuilder.buildQuarterlyOpportunityDealJson(deal);
    // Tool.pr("oppJson:\n" + oppJson);
    String ResponseJson = sfClient.sendComposite(oppJson);

    // get opportunity_deal logic should be
    List<String> sfOppIds = sfClient.extractObjectIds(ResponseJson, "Opportunity");

    return buildConcatenatedIds(sfOppIds);
  }

  /**
   * will need to return sfOppIds separated by ",", for the quarters that doesn't have sfOppId, will use ""
   * This will either require buildQuarterOpportunityDealJson to return which quarter has $ or parse the oppJson to find out
   * if there is no quarters at all, should return null
   */
  public String buildConcatenatedIds(List<String> sfOppIds) {
      int quarterAmounts[] = g.getQuarterAmounts();

      List<String> idsForQuarters = new ArrayList<>();
      int sfOppIdIndex = 0; // Index to keep track of the next sfOppId to use

      for (int amount : quarterAmounts) {
          if (amount == 0) {
              // Add an empty string for quarters with 0 amount
              idsForQuarters.add("");
          } else {
              // Add the next sfOppId, or an empty string if we've run out of IDs
              String idToAdd = sfOppIdIndex < sfOppIds.size() ? sfOppIds.get(sfOppIdIndex) : "";
              idsForQuarters.add(idToAdd);
              sfOppIdIndex++; // Move to the next sfOppId
          }
      }

      // Concatenate the IDs, separated by commas
      String concatenatedIds = String.join(",", idsForQuarters);

      // Return null if there are no quarters at all
      return concatenatedIds.isEmpty() ? null : concatenatedIds;
  }

  // For syncing, it is more complicated:
  // if existing sfOppId for the quarter, then must use it
  // if no existing sfOppId for the quarter:
  // if the quarter has $, then use the newly created opportunity
  // if the quarter has no $, then can use an empty string
  // sfOppIds are from the response of the composite call, only newly created opportunities
  // have sfOppIds. We insert the sfOppIds into 1. sfOppIdsMask[i] has "", 2. quarterAmounts[i] != 0
  public String buildSyncConcatenatedIds(List<String> sfOppIds) {
    int quarterAmounts[] = g.getQuarterAmounts();
    String sfOppIdsMask[] = g.getSfOppIdsMask();
    // boolean convertFirstQuarter = g.isConvertFirstQuarter();
    Tool.pr("sfOppIdsMask: " + String.join(",", sfOppIdsMask));
    Tool.pr("quarterAmounts: " + Arrays.stream(quarterAmounts)
                      .mapToObj(String::valueOf)
                      .collect(Collectors.joining(",")));
    List<String> idsForQuarters = new ArrayList<>();
    int sfOppIdIndex = 0; // Index to keep track of the next sfOppId to use

    for (int i = 0; i < quarterAmounts.length; i++) {
      int amount = quarterAmounts[i];
      if (!sfOppIdsMask[i].isEmpty()) {
        idsForQuarters.add(sfOppIdsMask[i]);
      } else {
        if (amount == 0) {
            idsForQuarters.add("");
        } else {
            // Add the next sfOppId, or an empty string if we've run out of IDs
            String idToAdd = sfOppIdIndex < sfOppIds.size() ? sfOppIds.get(sfOppIdIndex) : "";
            idsForQuarters.add(idToAdd);
            sfOppIdIndex++; // Move to the next sfOppId
        }
      }
    }

    // Concatenate the IDs, separated by commas
    String concatenatedIds = String.join(",", idsForQuarters);
    Tool.pr("concatenatedIds: " + concatenatedIds);

    // Return null if there are no quarters at all
    return concatenatedIds.isEmpty() ? null : concatenatedIds;
}  
  
  /**
   * Send a deal link which contains multiple single deal (registration) to Salesforce and return the sf_opportunity_id.
   * @dealLink: the deal link to be sent
   * @sfDealId: the sf_deal_id of the parent deal
   * @throws Exception
   */
  public String sendDealLink(DealLinkRecord dealLink, String sfDealId) throws Exception {
    // sfJsonBuilder.vMap.put("sf_deal_id", sfDealId);
    String oppJson = sfJsonBuilder.buildOpportunityDealLinkJson(dealLink);
    // Tool.pr("oppJson:\n" + oppJson);
    String ResponseJson = sfClient.sendComposite(oppJson);
    String sfOppId = sfClient.extractObjectId(ResponseJson, "Opportunity");
    return sfOppId;
  }
  
}
