package com.nbcu.psi.sf;

import com.nbcu.psi.Tool;
import com.nbcu.psi.BackupHashMap;
import com.nbcu.psi.SfRef;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

public class SfJsonBuilderBase {
    BackupHashMap<String, Object> vMap = new BackupHashMap<>();
    List<String> sfPropertyIds = new ArrayList<>();
    StringBuffer gsb = new StringBuffer();

    void reset() {
        vMap = new BackupHashMap<>();
        SfRef.reset();
        sfPropertyIds = new ArrayList<>();
        gsb = new StringBuffer();
    }

    String ref(String refType, String lookupId) {
        Tool.pf("ref(%s, %s)\n", refType, lookupId);
        SfRef ref = SfRef.getOrCreateRef(refType, lookupId);
        String refId = ref.referenceId;
        vMap.put("refid", refId);
        return refId;
    }

    private String debugTemplateRefs(String template) {
      StringBuilder debug = new StringBuilder();
      debug.append("Template content:\n" + template + "\n\n");
      
      debug.append("Current SfRef records:\n");
      for (SfRef ref : SfRef.records) {
          debug.append(String.format("- Type: %s, LookupId: %s, RefId: %s\n", 
              ref.refType, ref.lookupId, ref.referenceId));
      }
      
      debug.append("\nReference patterns found:\n");
      Pattern p = Pattern.compile("@\\{((\\w+)(\\()(\\w+)(\\)))?");
      Matcher m = p.matcher(template);
      while (m.find()) {
          debug.append(String.format("- Full match: %s\n", m.group(0)));
          debug.append(String.format("  Group 1: %s\n", m.group(1)));
          debug.append(String.format("  Group 2: %s\n", m.group(2)));
          debug.append(String.format("  Group 4: %s\n", m.group(4)));
      }
      
      return debug.toString();
    }

    String tpl(String refType, String lookupId) throws Exception {
        ref(refType, lookupId);
        String fname = Tool.f("api_tpl/sf_%s.json", refType);
        String tpl = Tool.readResourceFile(fname);
        String result = Tool.convertTemplate(tpl, vMap);
        
        // Add debug output
        Tool.pr("\n=== Template Reference Debug ===");
        Tool.pr(debugTemplateRefs(result));
        Tool.pr("===============================\n");
        
        return resolveSfRef(result);
    }    

    String resolveSfRef(String s) throws Exception {
        Tool.pr("\n*=== resolveSfRef Debug ===*\n");
        Tool.pf("resolveSfRef(%s)\n", s);
        Tool.pr("*=========================*\n");
        String pattern = "@\\{((\\w+)(\\()(\\w+)(\\)))?";
        String result = Pattern.compile(pattern).matcher(s).replaceAll(m -> {
            try {
                String refType = m.group(2);
                String lookupId = m.group(4);
                SfRef ref = SfRef.getRef(refType, lookupId);
                return "@{" + ref.referenceId;
            } catch (Exception e) {
                return m.group(1);
            }
        });
        return result;
    }

    String wrapComposite(String jsonStr) throws Exception {
        jsonStr = jsonStr.substring(0, jsonStr.lastIndexOf(","));
        Map<String, Object> mp = new HashMap<>();
        mp.put("content", jsonStr);
        return Tool.convertTemplate(
            Tool.readResourceFile("api_tpl/wrapper.json"), 
            mp
        );
    }

    void createPropertyRef(String sfPropertyId) throws Exception {
      if (!sfPropertyIds.contains(sfPropertyId)) {
        sfPropertyIds.add(sfPropertyId);
        gsb.append(tpl("product2_ref", sfPropertyId));
        gsb.append(tpl("pricebook_ref", sfPropertyId));
      }
    }

    String removeComments(String str) {
      return str.replaceAll("//.*\\n", "\n");
    }    
    
}