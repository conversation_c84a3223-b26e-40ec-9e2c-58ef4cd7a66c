package com.nbcu.psi.sf;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.net.HttpURLConnection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;  
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.Entity;
import javax.ws.rs.core.MediaType;
import java.net.URLEncoder;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nbcu.psi.Tool;
import org.glassfish.jersey.client.ClientConfig;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import lombok.NonNull;

/**
 * This class is used to send low level requests to Salesforce.
 */
@Service
public class SfClient {
  private Client client;
  private ClientConfig config;
  public String accessToken;
  public String instanceUrl;
  public String payloadLog;
  public String compositeUrl;

  @NonNull
  private Environment env;

  // Underlying HttpsURLConnection doesn't support PATCH, have to hack the code
  // https://stackify.dev/221857-patch-request-using-jersey-client
  public void hackJava() throws Exception {

    Field methodsField = HttpURLConnection.class.getDeclaredField("methods");
    methodsField.setAccessible(true);
    // get the methods field modifiers
    Field modifiersField = Field.class.getDeclaredField("modifiers");
    // bypass the "private" modifier 
    modifiersField.setAccessible(true);

    // remove the "final" modifier
    modifiersField.setInt(methodsField, methodsField.getModifiers() & ~Modifier.FINAL);

    /* valid HTTP methods */
    String[] methods = {
      "GET", "POST", "HEAD", "OPTIONS", "PUT", "DELETE", "TRACE", "PATCH"
    };
    // set the new methods - including patch
    methodsField.set(null, methods);

  }

  public SfClient(
    Environment env) throws Exception {
    this.env = env;
    hackJava();
    config = new ClientConfig();
    client = ClientBuilder.newClient(config);
  }

  public String getAndResetPayloadLog() {
    String log = payloadLog;
    payloadLog = "";
    return log;
  }

  public void printConfig() {
    Tool.pr("sfLoginUrl:" + env.getProperty("sf.login.url"));
    Tool.pr("sfLoginGrantType:" + env.getProperty("sf.login.grant_type"));
    Tool.pr("sfLoginClientId:" + env.getProperty("sf.login.client_id"));
    Tool.pr("sfLoginClientSecret:" + env.getProperty("sf.login.client_secret"));
    Tool.pr("sfLoginUsername:" + env.getProperty("sf.login.username"));
    Tool.pr("sfLoginPassword:" + env.getProperty("sf.login.password"));
  }

  /**
   * Retrieve instance_url and access_token from an initial sf url.
   * The initial sf url is configured in application.yml
   * In further access: instanceUrl will be used as url prefix, and access_token will be sent in header
   * @throws Exception
   */
  public void getAccessToken() throws Exception {
    try {
      String loginUrl = env.getProperty("sf.login.url");
      String responseStr = client.target(loginUrl)
        .queryParam("grant_type", env.getProperty("sf.login.grant_type"))
        .queryParam("client_id", env.getProperty("sf.login.client_id"))
        .queryParam("client_secret", env.getProperty("sf.login.client_secret"))
        .queryParam("username", env.getProperty("sf.login.username"))
        .queryParam("password", env.getProperty("sf.login.password"))
        .request(MediaType.APPLICATION_JSON_TYPE)
        .post(Entity.json(null), String.class);

      instanceUrl = Tool.readStrFromJson(responseStr, "instance_url");
      accessToken = Tool.readStrFromJson(responseStr, "access_token");
      compositeUrl = instanceUrl + "/services/data/v53.0/composite/";
      Tool.pr("SF accessToken:" + accessToken);
      Tool.pf("SF compositeUrl: %s", compositeUrl);
    } catch (Exception e) {
      throw new Exception("Failed to initiate connection to Salesforce", e);
    }
  }

  public String httpGet(String url) throws Exception {
    Tool.pr(url);
    return client.target(url)
      .request(MediaType.APPLICATION_JSON_TYPE)
      .header("Authorization", "Bearer " + accessToken)
      .get(String.class);
  }

  /**
   * Fetch all records from Salesforce by SOQL with handling pagination.
   * @param soql SOQL query string
   * @return List of records as key:value maps
   * @throws Exception if the request fails
   */
  public List<Map<String, Object>> fetchBySOQL(String soql) throws Exception {
    String encodedSoql = URLEncoder.encode(soql, "UTF-8");
    String url = instanceUrl + "/services/data/v53.0/query?q=" + encodedSoql;
    List<Map<String, Object>> allRecords = new ArrayList<>();

    String responseStr = httpGet(url);
    Map<String, Object> responseMap = Tool.parseJsonIntoHashMap(responseStr);
    List<Map<String, Object>> records = (List<Map<String, Object>>) responseMap.get("records");
    allRecords.addAll(records);

    // Handle pagination
    while (responseMap.containsKey("nextRecordsUrl")) {
      String nextRecordsUrl = (String) responseMap.get("nextRecordsUrl");
      responseStr = httpGet(instanceUrl + nextRecordsUrl);  // Full URL may need to be adjusted
      responseMap = Tool.parseJsonIntoHashMap(responseStr);
      records = (List<Map<String, Object>>) responseMap.get("records");
      allRecords.addAll(records);
    }

    // Remove 'attributes' key from every record
    for (Map<String, Object> record : allRecords) {
      record.remove("attributes");
    }    
    return allRecords;
  }

  /**
   * Fetch Salesforce Id by SOQL. Used to find out if a record exists in Salesforce.
   * @param soql
   * @return
   * @throws Exception
   */
  public String fetchSFIdBySOQL(String soql) throws Exception {
    String encodedSoql = URLEncoder.encode(soql, "UTF-8");
    String url = instanceUrl + "/services/data/v53.0/query" + "?q=" + encodedSoql;
    String responseStr = httpGet(url);
    Tool.pr(responseStr);
    Map<String, Object> rMap = Tool.parseJsonIntoHashMap(responseStr);
    List<Map<String, Object>> records = (List<Map<String, Object>>) rMap.get("records");
    if (records.size() == 0) {
      return null;
    } else {
      return (String) records.get(0).get("Id");
    }
  }

  /**
   * Fetch lineitem id by opportunity id. This is needed for resending.
   * @param opportunityId
   * @return
   * @throws Exception
   */
  public String fetchOpportunityLineitemId(String opportunityId) throws Exception {
    String soql = Tool.f("SELECT Id FROM OpportunityLineItem WHERE OpportunityId = '%s'", opportunityId);
    return fetchSFIdBySOQL(soql);
  }

  /**
   * Send a composite request to Salesforce.
   * This is a generic method, it can be used to send any composite request.
   */
  public String sendComposite(String requestJson) throws Exception {
    String wrappedStr = wrapComposite(requestJson);
    Tool.pr(wrappedStr);
    
    // Store payload before sending request
    payloadLog = wrappedStr;
    
    try {
        String responseStr = client.target(compositeUrl)
            .request(MediaType.APPLICATION_JSON_TYPE)
            .header("Authorization", "Bearer " + accessToken)
            .post(Entity.json(wrappedStr), String.class);
            
        String responseJson = Tool.prettyJson((responseStr));
        payloadLog += "\n\n\n\n\n\n\n\n\n\n\n\n";
        payloadLog += responseJson;
        handleCompositeResponse(responseStr);
        return responseJson;
        
    } catch (Exception e) {
        // For non-composite errors, still add error to payload
        payloadLog += "\n\n\n\n\n\n\n\n\n\n\n\n";
        payloadLog += Tool.getExceptionStr(e);
        throw e;
    }
  }
  
  /**
   * Test to see if the composite response contains http failure 400. If so, 
   * extract the failure block and raise SfApiException. This is considered logical error.
   * Other http error status will be handled by outer layer.
   * @param responseStr composite response
   * @throws Exception
   */
  private void handleCompositeResponse(String responseStr) throws Exception {
    // Tool.pr(responseStr);
    if (Pattern.matches(".*\"httpStatusCode\":400.*", responseStr)) {
      String failureBlock = getFailureBlock(responseStr);
      throw new SfApiException(failureBlock);
    }
  }


  /**
   * Extract single Object Id from composite response.
   * @param jsonResponse
   * @param objectType such as "Deal", "Opportunity"
   * @return
   * @throws Exception
   */
  public String extractObjectId(String jsonResponse, String objectType) throws Exception {
    List<String> ids = extractObjectIds(jsonResponse, objectType);
    if (ids.size() > 0) {
      return ids.get(0);
    } else {
      return null;
    }
  }

  /**
   * Due to the send split quarter request, the response may have multiple opportunity ids. Need to extract them all.
   * @param jsonResponse
   * @param objectType
   * @return
   * @throws Exception
   */
  public List<String> extractObjectIds(String jsonResponse, String objectType) throws Exception {
    Tool.pr(jsonResponse);
    List<String> ids = new ArrayList<>();

    // Parse JSON string into a HashMap
    Map<String, Object> jsonMap = Tool.parseJsonIntoHashMap(jsonResponse);

    // Navigate through the JSON structure
    List<Map<String, Object>> compositeResponse = (List<Map<String, Object>>) jsonMap.get("compositeResponse");

    // Pattern to match the ID in the Location field for the given object type
    Pattern pattern = Pattern.compile("/sobjects/" + objectType + "/(\\w+)");

    for (Map<String, Object> response : compositeResponse) {
      Map<String, Object> httpHeaders = (Map<String, Object>) response.get("httpHeaders");

      if (httpHeaders != null) {
        String location = (String) httpHeaders.get("Location");

        // Check if location is not null before proceeding
        if (location != null) {
            Matcher matcher = pattern.matcher(location);

            // If the pattern matches, add the ID to the list
            if (matcher.find()) {
                ids.add(matcher.group(1));
            }
        }
      }
    }

    return ids;
  }
  
  /**
   * Extract the failure block from multiple nodes in composite response.
   * Generally, there is only one node that has the real reason of the failture,
   * the other nodes are transaction rollback nodes.
   * @param responseStr composite response
   * @return failure block
   * @throws Exception
   */
  public String getFailureBlock(String responseStr) throws Exception{
    try {
      Map<String, Object> rMap = Tool.parseJsonIntoHashMap(responseStr);
      Tool.pr(rMap);
      for(Map node: ((List<Map>)rMap.get("compositeResponse"))) {
        String refId = (String)node.get("referenceId");
        Map body = (Map)((List)node.get("body")).get(0);
        body.put("refId", refId);
        String errorCode = (String)body.get("errorCode");
        if (!"PROCESSING_HALTED".equals(errorCode)) {
          return Tool.objToJson(body);
        }
      }
    } catch (Exception e) {
    }
    return responseStr;
  }


  /**
   * Wrap the payload with a composite request.
   * @throws Exception
   */
  public String wrapComposite(String jsonStr) throws Exception {
    Map<String, Object> mp = new HashMap<>();
    jsonStr = jsonStr.substring(0, jsonStr.lastIndexOf(","));
    mp.put("content", jsonStr);
    String fname = "api_tpl/wrapper.json";
    String tpl = Tool.readResourceFile(fname);
    String result = Tool.convertTemplate(tpl, mp);
    return result;
  }

  public String fetchDealPlatformAvailability(String sfPropertyId) throws Exception {
    String soql = String.format("SELECT Deal_Platform_Availability__c FROM Product2 WHERE Id = '%s'", sfPropertyId);
    String result = fetchSFFieldBySOQL(soql);
    if (result == null) {
      throw new Exception("No Deal_Platform_Availability__c found for Product2 with Id: " + sfPropertyId);
    }
    return result;
  }

  public String fetchSFFieldBySOQL(String soql) throws Exception {
    List<Map<String, Object>> records = fetchBySOQL(soql);
    if (records.isEmpty()) {
      return null;
    }
    Map<String, Object> record = records.get(0);
    return (String) record.values().iterator().next();
  }

}

