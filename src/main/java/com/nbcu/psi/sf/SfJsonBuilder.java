package com.nbcu.psi.sf;

import com.nbcu.psi.Tool;
import com.nbcu.psi.model.*;
import com.nbcu.psi.service.*;
import com.nbcu.psi.Global;
import org.springframework.stereotype.Service;
import org.springframework.core.env.Environment;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import java.time.LocalDate;
import java.util.Arrays;

@Service
@RequiredArgsConstructor
public class SfJsonBuilder extends SfJsonBuilderBase {
    @NonNull
    private BudgetService budgetService;
    @NonNull
    private BudgetYearService budgetYearService;
    @NonNull
    private SystemKeyAscService systemKeyAscService;
    @NonNull
    private QuarterDateCalculator quarterDateCalculator;
    @NonNull
    private SfClient sfClient;
    @NonNull
    private Global g;
    @NonNull
    private Environment environment;

    // Helper array for quarter names
    private final String[] quarters = {"pre", "q4", "q1", "q2", "q3", "post"};

    @Override
    public void reset() {
        super.reset();
        vMap.put("daypart_payload", "");
        vMap.put("deal_id_with_year", "");
        vMap.put("deal_link_id_with_year", "");
        populateConfigVariables();
    }

    private void populateConfigVariables() {
        String dealStagename = environment.getProperty("app.deal-stagename");
        vMap.put("deal_stagename", dealStagename);
    }

    public String buildDealJson(ParentDealRecord parent) throws Exception {
        reset();
        g.setParentDealRecord(parent);
        vMap.put("parent_deal_id_with_year", g.calculateParentDealId());
        vMap.put("parent_deal_name_with_year", g.calculateParentDealName());
        vMap.put("placeholder_dollars", parent.getPlaceholderDollar());
        populateBudgetMap();
        gsb.append(tpl("deal", "" + parent.getParentDealId()));
        return removeComments(gsb.toString());
    }

    public String buildOpportunityDealJson(DealRecord deal) throws Exception {
        g.setDealRecord(deal);
        reset();
        populateParentDealMap();
        vMap.put("deal_id_with_year", g.calculateDealId());
        populateBudgetMap();
        populateAmountMap(deal);
        createPropertyRef(g.sfProperty);
        gsb.append(tpl("opportunity_deal", "" + g.dealId));
        gsb.append(tpl("opportunity_lineitem_deal", "" + g.dealId));
        return removeComments(gsb.toString());
    }

    public String buildOpportunityDealLinkJson(DealLinkRecord dealLink) throws Exception {
        g.setDealLinkRecord(dealLink);
        reset();
        populateParentDealMap();
        vMap.put("deal_link_id_with_year", g.calculateDealLinkId());
        populateBudgetMap();
        populateDealLinkDataMap(dealLink);
        createPropertyRef(g.sfProperty);
        gsb.append(tpl("opportunity_deallink", "" + g.dealLinkId));
        gsb.append(tpl("opportunity_lineitem_deallink", "" + g.dealLinkId));
        
        for (DealRecord deal : dealLink.getDeals()) {
            if (!deal.isSingleOpportunity()) {
                populateDaypartsMap(dealLink, deal);
                gsb.append(tpl("daypart_deallink", "" + g.dealLinkId + "-" + deal.getDealId()));
            }
        }

        return removeComments(gsb.toString());
    }

    public String buildQuarterlyOpportunityDealJson(DealRecord deal) throws Exception {
        g.setDealRecord(deal);
        reset();
        populateParentDealMap();
        vMap.put("deal_id_with_year", g.calculateDealId());
        populateBudgetMap();
        populateAmountMap(deal);
        createPropertyRef(g.sfProperty);
        vMap.backup();
        
        for (int i = 0; i < quarters.length; i++) {
            appendQuarterJson(deal, i, false, false);
        }
        
        vMap.restore();
        return removeComments(gsb.toString());
    }

    public String buildSyncOpportunityDealJson(DealRecord deal) throws Exception {
        reset();
        g.setDealRecord(deal);
        populateParentDealMap();
        String oppId = deal.getSfOpportunityId();
        String lineitemId = sfClient.fetchOpportunityLineitemId(oppId);
        
        vMap.put("sf_opportunity_id", oppId);
        vMap.put("sf_opportunitylineitem_id", lineitemId);
        vMap.put("deal_id_with_year", g.calculateDealId());
        
        populateBudgetMap();
        populateAmountMap(deal);
        createPropertyRef(g.sfProperty);
        
        gsb.append(tpl("sync_opportunity_deal", "" + g.dealId));
        gsb.append(tpl("sync_opportunity_lineitem_deal", "" + g.dealId));
        
        return removeComments(gsb.toString());
    }

    public String buildSyncQuarterlyOpportunityDealJson(DealRecord deal) throws Exception {
        reset();
        g.setDealRecord(deal);
        populateParentDealMap();
        
        String sfOppIdStr = deal.getSfOpportunityId();
        boolean convertFirstQuarter = (sfOppIdStr.indexOf(",") == -1);
        g.setConvertFirstQuarter(convertFirstQuarter);
        
        String[] sfOppIds = processSyncOpportunityIds(sfOppIdStr, convertFirstQuarter, deal);
        g.setSfOppIdsMask(sfOppIds);
        
        populateBudgetMap();
        populateAmountMap(deal);
        createPropertyRef(g.sfProperty);
        vMap.backup();
        
        for (int i = 0; i < quarters.length; i++) {
            String sfOppId = sfOppIds[i];
            if (sfOppId.isEmpty()) {
                appendQuarterJson(deal, i, false, false);
            } else {
                String lineitemId = sfClient.fetchOpportunityLineitemId(sfOppId);
                vMap.put("sf_opportunity_id", sfOppId);
                vMap.put("sf_opportunitylineitem_id", lineitemId);
                vMap.put("deal_id_with_year", g.calculateDealId());
                appendQuarterJson(deal, i, true, true);
            }
        }
        
        vMap.restore();
        return removeComments(gsb.toString());
    }

    private void populateParentDealMap() {
        vMap.put("parent_deal_id_with_year", g.calculateParentDealId());
        vMap.put("sf_deal_id", g.getSfDealId());
    }

    private void populateBudgetMap() {
        vMap.put("sf_opportunity_id", g.budget.getSfOpportunityId());
        vMap.put("budget_id", g.budgetId);
        vMap.put("measurement_type", g.measurementTypeName);
        vMap.put("currency", g.currencyName);
        vMap.put("sf_agency", g.sfAgency);
        vMap.put("sf_advertiser", g.sfAdvertiser);
        vMap.put("sf_property", g.sfProperty);
        vMap.put("demographic_name", g.demographicName);
        vMap.put("deal_id", g.dealId);
        vMap.put("placeholder", g.isPlaceholder);
        vMap.put("marketplace_name", g.marketplaceName);
        vMap.put("property_type", g.propertyTypeName);
        vMap.put("is_digital", String.valueOf(g.isDigital));
        vMap.put("selling_vertical_name", g.sellingVerticalName);
        vMap.put("rating_stream_name", g.ratingStreamName);
        
        if (g.isUpfront) {
            vMap.put("season", g.getSeason());
        } else {
            vMap.put("fall_year", g.getDealYear());
        }
        
        vMap.put("buying_ae_sso_id", g.buyingAeSsoId);
        vMap.put("client_ae_sso_id", g.clientAeSsoId);
        vMap.put("planner_sso_id", g.plannerSsoId);
        vMap.put("account_manager_sso_id", g.accountManagerSsoId);
    }

    private void populateAmountMap(DealRecord dealRecord) throws Exception {
        vMap.put("amount", dealRecord.getActualAmount());
        vMap.put("stage", dealRecord.getStage());
        
        int quarterAmounts[] = new int[6];
        for (int i = 0; i < quarters.length; i++) {
            String quarter = quarters[i];
            String quarterAmountKey = "amount_" + quarter;
            long quarterAmount = getQuarterAmount(dealRecord, quarter);
            vMap.put(quarterAmountKey, quarterAmount);
            quarterAmounts[i] = (int)quarterAmount;
        }
        
        populateStartEnd(quarterAmounts);
        g.setQuarterAmounts(quarterAmounts);
        
        String quarterStr = amountsToQuarter(quarterAmounts);
        String dealName = g.calculateDealName(quarterStr);
        vMap.put("deal_name", dealName);
    }

    private void populateDealLinkDataMap(DealLinkRecord dl) throws Exception {
        String sfdcPropertyId = dl.getSfdcPropertyId();
        if (Tool.validSfId(sfdcPropertyId)) {
            vMap.put("sf_property", sfdcPropertyId);
        }
        
        vMap.put("deal_link_id", dl.getDealLinkId());
        vMap.put("deal_link_name", dl.getDealLinkName());
        vMap.put("stage", dl.getStage());
        vMap.put("amount", dl.getActualAmount());
        
        int quarterAmounts[] = new int[6];
        for (int i = 0; i < quarters.length; i++) {
            String quarter = quarters[i];
            String quarterAmountKey = "amount_" + quarter;
            long quarterAmount = getQuarterAmount(dl, quarter);
            vMap.put(quarterAmountKey, quarterAmount);
            quarterAmounts[i] = (int)quarterAmount;
        }
        
        populateStartEnd(quarterAmounts);
    }

    private void populateDaypartsMap(DealLinkRecord dealLink, DealRecord deal) {
        vMap.put("sf_vertical_name", deal.getSfdcVerticalName());
        vMap.put("sf_vertical", "");
        vMap.put("amount_pre", deal.getActualPrequarterAmount());
        vMap.put("amount_q4", deal.getActualQuarter4Amount());
        vMap.put("amount_q1", deal.getActualQuarter1Amount());
        vMap.put("amount_q2", deal.getActualQuarter2Amount());
        vMap.put("amount_q3", deal.getActualQuarter3Amount());
        vMap.put("amount_post", deal.getActualPostquarterAmount());
    }

    private String[] processSyncOpportunityIds(String sfOppIdStr, boolean convertFirstQuarter, DealRecord deal) {
        String[] sfOppIds = sfOppIdStr.split(",", -1);
        
        if (convertFirstQuarter) {
            sfOppIds = new String[]{"", "", "", "", "", ""};
            for (int i = 0; i < quarters.length; i++) {
                long qAmount = getQuarterAmount(deal, quarters[i]);
                if (qAmount > 0) {
                    sfOppIds[i] = sfOppIdStr;
                    break;
                }
            }
        }
        
        return sfOppIds;
    }

    private void appendQuarterJson(DealRecord deal, int i, boolean isUpdate, boolean forceUpdate) throws Exception {
        long qAmount = getQuarterAmount(deal, quarters[i]);
        
        if (qAmount > 0 || forceUpdate) {
            int[] quarterAmounts = new int[6];
            quarterAmounts[i] = (int)qAmount;
            LocalDate[] startEndDate = getStartEndDate(quarterAmounts);
            
            String dealIdQuarter = deal.getDealId() + quarters[i];
            vMap.put("deal_id_quarter", dealIdQuarter);
            vMap.put("deal_id_with_year", g.withYear(deal.getDealId()) + "-" + quarters[i]);
            vMap.put("start_date", startEndDate[0]);
            vMap.put("end_date", startEndDate[1]);
            
            vMapSetAmountZero();
            vMap.put("amount_" + quarters[i], qAmount);
            vMap.put("amount", qAmount);
            
            String quarterStr = amountsToQuarter(quarterAmounts);
            String dealName = g.calculateDealName(quarterStr);
            vMap.put("deal_name", dealName);
            
            if (isUpdate) {
                gsb.append(tpl("sync_opportunity_deal_quarter", dealIdQuarter));
                gsb.append(tpl("sync_opportunity_lineitem_deal_quarter", dealIdQuarter));
            } else {
                gsb.append(tpl("opportunity_deal_quarter", dealIdQuarter));
                gsb.append(tpl("opportunity_lineitem_deal_quarter", dealIdQuarter));
            }
        }
    }

    private void vMapSetAmountZero() {
        Arrays.stream(quarters).forEach(q -> vMap.put("amount_" + q, 0));
        vMap.put("amount", 0);
    }

    private void populateStartEnd(int[] quarterAmounts) throws Exception {
        LocalDate[] startEndDate = getStartEndDate(quarterAmounts);
        vMap.put("start_date", startEndDate[0]);
        vMap.put("end_date", startEndDate[1]);
    }

    private LocalDate[] getStartEndDate(int[] quarterAmounts) throws Exception {
        return quarterDateCalculator.getStartEnd(g.budgetYearName, quarterAmounts, (int)g.propertyId, g.isDigital);
    }

    private String amountsToQuarter(int[] quarterAmounts) {
        String[] quarterNames = g.getQuarterNames();
        int firstNoneZero = -1;
        int lastNoneZero = -1;
        
        for (int i = 0; i < quarterAmounts.length; i++) {
            if (quarterAmounts[i] > 0) {
                if (firstNoneZero == -1) {
                    firstNoneZero = i;
                }
                lastNoneZero = i;
            }
        }
        
        if (firstNoneZero == -1) return "";
        if (firstNoneZero == lastNoneZero) return quarterNames[firstNoneZero];
        return quarterNames[firstNoneZero] + "-" + quarterNames[lastNoneZero];
    }

    private long getQuarterAmount(DealRecord deal, String quarter) {
      switch (quarter) {
          case "pre":
              return deal.getActualPrequarterAmount();
          case "q4":
              return deal.getActualQuarter4Amount();
          case "q1":
              return deal.getActualQuarter1Amount();
          case "q2":
              return deal.getActualQuarter2Amount();
          case "q3":
              return deal.getActualQuarter3Amount();
          case "post":
              return deal.getActualPostquarterAmount();
          default:
              return 0;
      }
  }

  private long getQuarterAmount(DealLinkRecord dl, String quarter) {
      switch (quarter) {
          case "pre":
              return dl.getActualPrequarterAmount();
          case "q4":
              return dl.getActualQuarter4Amount();
          case "q1":
              return dl.getActualQuarter1Amount();
          case "q2":
              return dl.getActualQuarter2Amount();
          case "q3":
              return dl.getActualQuarter3Amount();
          case "post":
              return dl.getActualPostquarterAmount();
          default:
              return 0;
      }
  }


}