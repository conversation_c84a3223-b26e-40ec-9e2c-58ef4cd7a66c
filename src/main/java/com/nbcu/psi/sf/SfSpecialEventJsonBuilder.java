package com.nbcu.psi.sf;

import com.nbcu.psi.Tool;
import com.nbcu.psi.SpecialEventContext;
import com.nbcu.psi.model.*;
import com.nbcu.psi.service.BudgetYearService;
import com.nbcu.psi.PamUtils;
import org.springframework.stereotype.Service;
import org.springframework.core.env.Environment;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import java.util.*;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;

@Service
@RequiredArgsConstructor
public class SfSpecialEventJsonBuilder extends SfJsonBuilderBase {
    @NonNull
    private final SfClient sfClient;
    @NonNull
    private final PamUtils pamUtils;

    @NonNull private final SpecialEventQuarterHandler quarterHandler;

    @NonNull private final BudgetYearService budgetYearService;

    @NonNull private final Environment environment;
    
    private SpecialEventContext context;
    private static final SimpleDateFormat SF_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    public void setContext(SpecialEventContext context) {
        this.context = context;
    }

    @Override
    public void reset() {
        super.reset();
        populateConfigVariables();
    }

    private void populateConfigVariables() {
        String dealStagename = environment.getProperty("app.deal-stagename");
        vMap.put("deal_stagename", dealStagename);
    }

    private String formatDateForSalesforce(Date date) {
        if (date == null) {
            return null;
        }
        return SF_DATE_FORMAT.format(date);
    }

    public String buildSpecialEventDeal() throws Exception {
        validateContext();
        reset();
        populateEventMap();
        populateCommonDealMap();
        String seIdWithSeDealId = buildSeIdWithSeDealId();
        vMap.put("se_id_with_se_deal_id", seIdWithSeDealId);
        vMap.put("se_deal_name", buildSeDealName());
        gsb.append(tpl("se_deal", String.valueOf(context.getEventDeal().getSpecialEventDealId())));
        return removeComments(gsb.toString());
    }

    public String buildOpportunityJson(SpecialEventDealDetail detail) throws Exception {
        validateContext();
        if (detail == null) {
            throw new IllegalArgumentException("Detail cannot be null");
        }
        reset();
        populateEventMap();
        populateCommonDealMap();
        populateDetailMap(detail);
        
        String dealName = buildDealName();
        vMap.put("deal_name", dealName);
        
        String seIdWithSeDealId = buildSeIdWithSeDealId();
        vMap.put("se_id_with_se_deal_id", seIdWithSeDealId);
        
        String seIdWithSeDetailId = buildSeIdWithSeDetailId(detail);
        vMap.put("se_id_with_se_detail_id", seIdWithSeDetailId);
        
        createPropertyRef(context.getSfPropertyId());
        
        String detailId = String.valueOf(detail.getSpecialEventDealDetailId());
        vMap.put("detail_id", detailId);
        
        // Format dates properly for Salesforce
        vMap.put("start_date", formatDateForSalesforce(context.getStartDate()));
        vMap.put("end_date", formatDateForSalesforce(context.getEndDate()));

        // vMap.put("product_category_name", context.getProductCategoryName());
        vMap.put("selling_vertical_name", context.getSellingVertical().getSellingVerticalName());

        // Set budget amounts
        BigDecimal budgetAmount = detail.getBudgetDollars();
        vMap.put("amount", budgetAmount);
        vMap.put("budget_dollars", budgetAmount);

        gsb.append(tpl("se_opportunity", detailId));
        gsb.append(tpl("se_opportunity_lineitem", detailId));
        
        return removeComments(gsb.toString());
    }

    public String buildSyncOpportunityJson(SpecialEventDealDetail detail) throws Exception {
        validateContext();
        if (detail == null) {
            throw new IllegalArgumentException("Detail cannot be null");
        }
        reset();
        populateEventMap();
        populateCommonDealMap();
        populateDetailMap(detail);

        String dealName = buildDealName();
        vMap.put("deal_name", dealName);
        vMap.put("sf_opportunity_id", detail.getSfOpportunityId());
        
        String lineItemId = sfClient.fetchOpportunityLineitemId(detail.getSfOpportunityId());
        vMap.put("sf_opportunitylineitem_id", lineItemId);
        
        // Format dates properly for Salesforce
        vMap.put("start_date", formatDateForSalesforce(context.getStartDate()));
        vMap.put("end_date", formatDateForSalesforce(context.getEndDate()));
        
        // Set budget amounts
        BigDecimal budgetAmount = detail.getBudgetDollars();
        vMap.put("amount", budgetAmount);
        vMap.put("budget_dollars", budgetAmount);
        
        createPropertyRef(context.getSfPropertyId());
        
        String detailId = String.valueOf(detail.getSpecialEventDealDetailId());
        gsb.append(tpl("se_sync_opportunity", detailId));
        gsb.append(tpl("se_sync_opportunity_lineitem", detailId));
        
        return removeComments(gsb.toString());
    }

    private void validateContext() {
        if (context == null) {
            throw new IllegalStateException("Context must be set before building");
        }
        context.validate(); // Validates all required fields are present
    }

    private void populateEventMap() {
        SpecialEvent event = context.getSpecialEvent();
        vMap.put("special_event_id", event.getSpecialEventId());
        vMap.put("special_event_name", event.getSpecialEventName());
        vMap.put("special_event_type_name", event.getSpecialEventType().getSpecialEventTypeName());
        vMap.put("start_date", formatDateForSalesforce(event.getStartDate()));
        vMap.put("end_date", formatDateForSalesforce(event.getEndDate()));
        vMap.put("placeholder", false);
        vMap.put("placeholder_dollars", 0);
    }

    private void populateCommonDealMap() throws Exception {
      // Core entity mappings
      vMap.put("sf_advertiser", context.getSfAdvertiserId());
      vMap.put("sf_agency", context.getSfAgencyId());
      vMap.put("sf_deal_id", context.getSfDealId());

      // User SSO IDs
      AppUser buyingAe = context.getBuyingAppUser();
      AppUser clientAe = context.getClientAppUser();
      AppUser accountManager = context.getAccountManagerAppUser();
      vMap.put("buying_ae_sso_id", buyingAe != null ? buyingAe.getSsoId().toString() : "");
      vMap.put("client_ae_sso_id", clientAe != null ? clientAe.getSsoId().toString() : "");
      vMap.put("account_manager_sso_id", accountManager != null ? accountManager.getSsoId().toString() : "");

      // Core attributes
      vMap.put("demographic_name", context.getDemographicName());
      vMap.put("rating_stream_name", pamUtils.convertRatingStreamName(context.getRatingStreamName()));
      vMap.put("sponsorship_details", context.getSponsorshipDetails());

      // Property type handling - only if property is available
      Property property = context.getProperty();
      if (property != null) {
          try {
              String propertyType = pamUtils.convertPropertyType(property);
              vMap.put("property_type", propertyType);
              vMap.put("is_digital", context.isDigitalProperty());
          } catch (Exception e) {
              // Log warning but continue - property type is optional for initial deal creation
              Tool.pr("Warning: Could not determine property type: " + e.getMessage());
              vMap.put("property_type", "");
              vMap.put("is_digital", false);
          }
      } else {
          vMap.put("property_type", "");
          vMap.put("is_digital", false);
      }

      // Quarter handling setup
      quarterHandler.initializeForEvent(context.getSpecialEvent());
      vMap.put("season", quarterHandler.getSeason());
      vMap.put("year_season", quarterHandler.getYearSeason());
      vMap.put("deal_year", quarterHandler.getDealYear());

      // Measurement and currency
      vMap.put("measurement_type", context.getMeasurementTypeName());
      vMap.put("currency", context.getCurrencyName());

      // Status fields
      vMap.put("stage", context.getStage());

      // Initialize quarter amounts to 0
      vMap.put("amount_pre", 0);
      vMap.put("amount_q1", 0);
      vMap.put("amount_q2", 0);
      vMap.put("amount_q3", 0);
      vMap.put("amount_q4", 0);
      vMap.put("amount_post", 0);

      // Convert marketplace name if available
      Marketplace marketplace = context.getMarketplace();
      if (marketplace != null) {
          String convertedMarketplaceName = pamUtils.convertMarketplaceName(marketplace.getMarketplaceName());
          vMap.put("marketplace_name", convertedMarketplaceName);
      } else {
          vMap.put("marketplace_name", "Broadcast Upfront");
      }

      // Handle season/fall_year based on marketplace
      if (marketplace != null && marketplace.getMarketplaceName() != null) {
          String marketplaceName = marketplace.getMarketplaceName().toLowerCase();
          boolean isUpfront = marketplaceName.contains("Upfront") || marketplaceName.contains("Olympics");
          
          if (isUpfront) {
              vMap.put("season", quarterHandler.getSeason());
          } else {
              vMap.put("fall_year", quarterHandler.getDealYear());
          }
      } else {
          // Default to fall_year if no marketplace info
          vMap.put("fall_year", quarterHandler.getDealYear());
      }

      // Add planner SSO ID if available
      AppUser planner = context.getPlannerAppUser();
      if (planner != null && planner.getSsoId() != null) {
          vMap.put("planner_sso_id", planner.getSsoId().toString());
      }
    }

    private void populateDetailMap(SpecialEventDealDetail detail) throws Exception {
        vMap.put("sf_property", context.getSfPropertyId());
        BigDecimal budgetAmount = detail.getBudgetDollars();
        // Use quarter handler to populate amounts
        quarterHandler.populateQuarterAmount(detail, vMap);        
        vMap.put("amount", budgetAmount);
        vMap.put("budget_dollars", budgetAmount);
        vMap.put("PAM_Total_Budget__c", budgetAmount);
        
        // Ensure measurement type and currency are available for all templates
        vMap.put("measurement_type", context.getMeasurementTypeName());
        vMap.put("currency", context.getCurrencyName());
    }

    private String buildDealName() {
        return String.format("%s - %s - %s",
            context.getAdvertiser().getAdvertiserName(),
            context.getSpecialEvent().getSpecialEventName(),
            context.getProperty().getPropertyName());
    }

    private String buildSeIdWithSeDealId() {
        return String.format("se-%d-%d",
            context.getSpecialEvent().getSpecialEventId(),
            context.getEventDeal().getSpecialEventDealId());
    }

    private String buildSeIdWithSeDetailId(SpecialEventDealDetail detail) {
        return String.format("se-%d-%d",
            context.getSpecialEvent().getSpecialEventId(),
            detail.getSpecialEventDealDetailId());
    }

    private String buildSeDealName() {
        return String.format("%s-%s",
            context.getAdvertiser().getAdvertiserName(),
            context.getSpecialEvent().getSpecialEventName());
    }
}