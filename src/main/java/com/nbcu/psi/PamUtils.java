package com.nbcu.psi;

import com.nbcu.psi.model.*;
import com.nbcu.psi.service.*;
import com.nbcu.psi.sf.*;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class PamUtils {
    @NonNull
    private final SystemKeyAscService systemKeyAscService;
    
    @NonNull
    private final SfClient sfClient;

    public String getAdvertiserSfId(Advertiser advertiser) throws Exception {
        try {
            return systemKeyAscService.findByAdvertiserId(advertiser.getAdvertiserId()).getExternalSystemKey();
        } catch (Exception e) {
            throw new SfApiException("Failed to retrieve Salesforce Id from systemKeyAsc for " + advertiser);
        }
    }

    public String getAgencySfId(Agency agency) throws Exception {
        try {
            return systemKeyAscService.findByAgencyId(agency.getAgencyId()).getExternalSystemKey();
        } catch (Exception e) {
            throw new SfApiException("Failed to retrieve Salesforce Id from systemKeyAsc for " + agency);
        }
    }

    public String getPropertySfId(Property property) throws Exception {
        Map<String, String> map = Map.of(
            "Olympics",  "Olympics",
            "Olympics Digital", "Olympics - Video",
            "World Cup", "Telemundo - FIFA Women\\'s World Cup",
            "World Cup Digital", "FIFA World Cup"
        );

        String replacePropertyName = map.get(property.getPropertyName());
        if (replacePropertyName != null) {
            try {
                String soql = Tool.f("SELECT Id FROM Product2 WHERE Name = '%s'", replacePropertyName);
                return sfClient.fetchSFIdBySOQL(soql);
            } catch (Exception e) {
                throw new SfApiException("Failed to query Salesforce Id from SOQL for " + replacePropertyName);
            }
        } else {
            try {
                return systemKeyAscService.findByPropertyId(property.getPropertyId()).getExternalSystemKey();
            } catch (Exception e) {
                throw new SfApiException("Failed to retrieve Salesforce Id from systemKeyAsc for " + property);
            }
        }
    }

    public String convertPropertyType(Property property) throws Exception {
        String sfPropertyId = getPropertySfId(property);
        String dealPlatformAvailability = sfClient.fetchDealPlatformAvailability(sfPropertyId);
        
        Map<String, String> propertyTypeMap = Map.of(
            "One Platform (Linear)", "NBCU Ad Sales Linear",
            "One Platform (Digital)", "NBCU Ad Sales Digital",
            "Optimized Linear", "AdSmart",
            "Programmatic", "NBCU Ad Sales Digital",
            "Global", "Global Opportunity"
        );
        
        String propertyType = propertyTypeMap.get(dealPlatformAvailability);
        if (propertyType == null) {
            propertyType = Tool.f("Unknown Property Type for Deal_Platform_Availability__c: %s", dealPlatformAvailability);
        }
        return propertyType;
    }

    public String convertRatingStreamName(String ratingStreamName) {
        Map<String, String> map = Map.of(
            "L+SD",  "PLSD",
            "UNMAPPED", ""
        );
        String result = map.get(ratingStreamName);
        return result != null ? result : ratingStreamName;
    }

    public String convertMarketplaceName(String marketplaceName) {
        Map<String, String> map = Map.of(
            "Scatter",  "Scatter",
            "Calendar", "Calendar Upfront",
            "Upfront",  "Broadcast Upfront",
            "Olympics", "Broadcast Upfront"
        );
        String result = map.get(marketplaceName);
        return result != null ? result : marketplaceName;
    }

    public String convertSellingVerticalName(String sellingVerticalName) {
        Map<String, String> map = Map.ofEntries(
            Map.entry("Broadcast Entertainment", "Broadcast Entertainment"),
            Map.entry("CODE",                    "CODE"),
            Map.entry("ELG",                     "ELG"),
            Map.entry("Hispanic",                "Hispanic"),
            Map.entry("News",                    "News"),
            Map.entry("Portfolio",               "NBCU AdSmart"),
            Map.entry("Digital",                 "One Digital Video"),
            Map.entry("Peacock",                 "Peacock"),
            Map.entry("Sports",                  "Sports/Olympics"),
            Map.entry("Syndication",             "Syndication"),
            Map.entry("Local",                   "TBD")
        );
        String result = map.get(sellingVerticalName);
        return result != null ? result : sellingVerticalName;
    }
}