package com.nbcu.psi;

import com.nbcu.psi.model.*;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;

/**
 * Holds the complete context of a special event deal being processed,
 * including all related entities needed for Salesforce payload generation.
 */
@Getter 
@Setter
public class SpecialEventContext {
    // Core entities
    private SpecialEvent specialEvent;
    private SpecialEventDeal eventDeal;
    private Advertiser advertiser;
    private Agency agency;
    private Property property;
    private Demographic demographic;
    private AppUser buyingAppUser;
    private AppUser clientAppUser;
    private AppUser planningAppUser;
    private AppUser plannerAppUser;
    private AppUser linearPlannerAppUser;
    private AppUser digitalPlannerAppUser;
    private AppUser accountManagerAppUser;
    private MeasurementType measurementType;
    private Currency currency;
    private RatingStream dealRatingStream;
    private RatingStream defaultRatingStream;
    private ProductCategory productCategory;
    private SellingVertical sellingVertical;

    // Salesforce IDs
    private String sfAdvertiserId;
    private String sfAgencyId;
    private String sfPropertyId;
    private String sfDealId;
    private String sfOpportunityId;

    // Status and cached values
    private String dealName;
    private boolean digital;
    private String stage;
    
    // Add new fields
    private Marketplace marketplace;
    
    /**
     * Builds the special event deal name following the format:
     * [advertiser_name] - [special_event_name] - [property_name]
     */
    public String buildDealName() {
        if (dealName == null) {
            dealName = String.format("%s - %s - %s",
                advertiser != null ? advertiser.getAdvertiserName() : "Unknown Advertiser",
                specialEvent != null ? specialEvent.getSpecialEventName() : "Unknown Event",
                property != null ? property.getPropertyName() : "Unknown Property");
        }
        return dealName;
    }

    /**
     * Builds the special event ID with deal ID in the format used for Salesforce integration
     */
    public String buildSpecialEventDealId() {
        return String.format("se-%d-%d",
            specialEvent != null ? specialEvent.getSpecialEventId() : 0,
            eventDeal != null ? eventDeal.getSpecialEventDealId() : 0);
    }

    /**
     * Builds the special event ID with detail ID in the format used for Salesforce integration
     */
    public String buildSpecialEventDetailId(SpecialEventDealDetail detail) {
        return String.format("se-%d-%d",
            specialEvent != null ? specialEvent.getSpecialEventId() : 0,
            detail != null ? detail.getSpecialEventDealDetailId() : 0);
    }

    /**
     * Gets the deal start date, defaulting to special event start date
     */
    public Date getStartDate() {
        return specialEvent != null ? specialEvent.getStartDate() : null;
    }

    /**
     * Gets the deal end date, defaulting to special event end date
     */
    public Date getEndDate() {
        return specialEvent != null ? specialEvent.getEndDate() : null;
    }

    /**
     * Gets the measurement type name with null safety
     */
    public String getMeasurementTypeName() {
        return measurementType != null ? measurementType.getMeasurementTypeName() : "";
    }

    /**
     * Gets the currency name with null safety
     */
    public String getCurrencyName() {
        return currency != null ? currency.getCurrencyName() : "";
    }

    /**
     * Gets the demographic name with null safety
     */
    public String getDemographicName() {
        return demographic != null ? demographic.getDemographicName() : "";
    }

    /**
     * Gets the rating stream name with null safety
     */
    public String getRatingStreamName() {
      // Prefer deal's rating stream over default
        RatingStream ratingStream = dealRatingStream != null ? dealRatingStream : defaultRatingStream;
        return ratingStream != null ? ratingStream.getRatingStreamName() : "L+SD";
    }

    public String getProductCategoryName() {
      return productCategory != null ? productCategory.getProductCategoryName() : "";
  }


    /**
     * Gets the buying app user's SSO ID with null safety
     */
    public String getBuyingAppUserSsoId() {
        return buyingAppUser != null && buyingAppUser.getSsoId() != null ? 
            buyingAppUser.getSsoId().toString() : "";
    }

    /**
     * Gets the client app user's SSO ID with null safety
     */
    public String getClientAppUserSsoId() {
        return clientAppUser != null && clientAppUser.getSsoId() != null ? 
            clientAppUser.getSsoId().toString() : "";
    }

    /**
     * Gets the planning app user's SSO ID with null safety
     */
    public String getPlanningAppUserSsoId() {
        return planningAppUser != null && planningAppUser.getSsoId() != null ? 
            planningAppUser.getSsoId().toString() : "";
    }

    /**
     * Gets the planner app user's SSO ID with null safety
     */
    public String getPlannerAppUserSsoId() {
        return plannerAppUser != null && plannerAppUser.getSsoId() != null ? 
            plannerAppUser.getSsoId().toString() : "";
    }    

    /**
     * Gets the account manager app user's SSO ID with null safety
     */
    public String getAccountManagerAppUserSsoId() {
        return accountManagerAppUser != null && accountManagerAppUser.getSsoId() != null ? 
            accountManagerAppUser.getSsoId().toString() : "";
    }

    /**
     * Determines if this is a digital property
     */
    public boolean isDigitalProperty() {
        if (property != null && property.getPropertyType() != null) {
            return "Digital".equals(property.getPropertyType().getPropertyTypeName());
        }
        return false;
    }

    /**
     * Gets the sponsorship details with null safety
     */
    public String getSponsorshipDetails() {
        return eventDeal != null ? eventDeal.getSponsorshipDetails() : "";
    }

    /**
     * Validates that all required fields are present
     */
    public void validate() {
      if (specialEvent == null) throw new IllegalStateException("Special Event is required");
      if (eventDeal == null) throw new IllegalStateException("Special Event Deal is required");
      if (advertiser == null) throw new IllegalStateException("Advertiser is required");
      if (agency == null) throw new IllegalStateException("Agency is required");
      if (demographic == null) throw new IllegalStateException("Demographic is required");
      if (measurementType == null) throw new IllegalStateException("Measurement Type is required");
      if (currency == null) throw new IllegalStateException("Currency is required");
      if (productCategory == null) throw new IllegalStateException("Product Category is required");
  }
}