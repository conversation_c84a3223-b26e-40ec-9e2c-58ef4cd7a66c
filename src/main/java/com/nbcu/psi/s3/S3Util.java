package com.nbcu.psi.s3;
import com.nbcu.psi.service.*;
import com.nbcu.psi.Global;
import com.nbcu.psi.Tool;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.PutObjectRequest;

import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

/**
 * S3Util allows us to read and write files to S3 for logging payloads and
 * storing last_processed_date.
 * There is a setting in application.yml called islocal if set to true will 
 * read and write to local file system tmp folder. Added this feature because
 * we can't write to S3 from local machine any more.
 */
@Service
@RequiredArgsConstructor
public class S3Util {

  @NonNull
  private Global g;

  @NonNull
  private Environment env;

  private final AmazonS3 s3Client;

  public String readStr(String fname) {
    try {
      if (g.isLocal()) {
        String f = "tmp" + "/" + fname;
        // Tool.pf("readStr(%s)", f);
        return Tool.fileRead(f);
      } else {
        String path = getFolder() + "/" + fname;
        return s3Client.getObjectAsString(getBucket(), path);
      }
    } catch (Exception e) {
      return null;
    }
  }

  public void deleteFile(String fname) {
    String path = getFolder() + "/" + fname;
    s3Client.deleteObject(getBucket(), path);
  }

  public void writeStr(String fname, String str) {
    if (g.isLocal()) {
      String f = "tmp" + "/" + fname;
      // Tool.pf("writeStr(%s, %s)", f, str);
      Tool.fileWriteNoException(f, str);
    } else {
      String path = getFolder() + "/" + fname;
      s3Client.putObject(getBucket(), path, str);
      s3Client.setObjectAcl(getBucket(), path, CannedAccessControlList.BucketOwnerFullControl);
    }
  }

  public String getBucket() {
    return env.getProperty("s3.bucket");
  }

  public String getFolder() {
    return env.getProperty("s3.bucket-path");
  }

  public String getFullPath(String path) {
    return String.format("s3://%s/%s/%s", getBucket(), getFolder(), path);
  }
}
