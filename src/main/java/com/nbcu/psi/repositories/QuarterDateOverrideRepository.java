package com.nbcu.psi.repositories;

import java.math.BigDecimal;

import com.nbcu.psi.model.QuarterDateOverride;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface QuarterDateOverrideRepository extends JpaRepository<QuarterDateOverride, Long>{
  public static String queryFindByFinanceQuarterAndProperty =
    " select * " +
    " from {h-schema}quarter_date_override " +
    " where finance_quarter_id = ?1 " +
    " and property_id = ?2 ";

  @Query(nativeQuery = true, value = queryFindByFinanceQuarterAndProperty)
  QuarterDateOverride findByFinanceQuarterIdAndPropertyId(BigDecimal financeQuarterId, BigDecimal propertyId);
}


