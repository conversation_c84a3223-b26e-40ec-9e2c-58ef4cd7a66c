package com.nbcu.psi.repositories;

import com.nbcu.psi.model.SpecialEventDealDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import java.util.List;

public interface SpecialEventDealDetailRepository extends JpaRepository<SpecialEventDealDetail, Long> {
    
    List<SpecialEventDealDetail> findBySpecialEventDealId(long specialEventDealId);
    
    @Query(value = "SELECT * FROM SPECIAL_EVENT_DEAL_DETAIL " +
           "WHERE SF_SYNC_STATUS = :status " + 
           "AND SF_SYNC_AT > to_date(:lastSyncDate, 'YYYY-MM-DD HH24:MI:SS') " +
           "AND PROPERTY_ID != (select PROPERTY_ID from PROPERTY where PROPERTY_NAME in ('Portfolio Spend'))", 
           nativeQuery = true)
    List<SpecialEventDealDetail> findByStatusAndLastSyncDate(String status, String lastSyncDate);
}