package com.nbcu.psi.repositories;
import java.util.List;
import com.nbcu.psi.model.DealLink;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
public interface DealLinkRepository 
    extends JpaRepository<DealLink, Long>{
    public static String queryFindInsertDealLinks =
        " select " + 
        "   dl.deal_link_id, " + 
        "   dl.deal_link_name, " +
        "   dlt.single_opportunity, " +
        "   d.property_id, " +
        "   b.budget_id, " + 
        "   sf_v.sfdc_vertical_id, " +
        "   sf_v.sfdc_vertical_name, " +
        "   sf_v.sfdc_property_id, " +
        "   b.actual_prequarter_amount, " +
        "   b.actual_quarter4_amount, " +
        "   b.actual_quarter1_amount, " +
        "   b.actual_quarter2_amount, " +
        "   b.actual_quarter3_amount, " +
        "   b.actual_postquarter_amount, " +
        "   b.sf_deal_id, " +
        "   b.sf_opportunity_id " +
        " from {h-schema}deal_link dl " +
        " left join {h-schema}deal_link_asc dla on dl.deal_link_id = dla.deal_link_id " +
        " left join {h-schema}deal d on dla.deal_id = d.deal_id " +
        " join {h-schema}deal_link_type dlt on dlt.deal_link_type_id = dl.deal_link_type_id and dlt.send_to_salesforce = 1 " +
        " left join {h-schema}budget b on d.deal_id = b.deal_id " +
        "      and b.budget_year_id = (select budget_year_id from {h-schema}budget_year where default_budget_year = 1) " +
        " left join {h-schema}sfdc_intg_vertical sf_v ON d.sfdc_intg_vertical_id = sf_v.sfdc_intg_vertical_id " +
        " where d.deal_id is not null and b.budget_id is not null " +
        "   and b.send_to_salesforce = 1 " +
        "   and (b.updated_at > to_date(?1, 'YYYY-MM-DD HH24:MI:SS') or  " +
        "        dla.updated_at > to_date(?1, 'YYYY-MM-DD HH24:MI:SS')) ";

    @Query(nativeQuery = true, value = queryFindInsertDealLinks)
    List<Object[]> findInsertDealLinks(String dateStr);
}


