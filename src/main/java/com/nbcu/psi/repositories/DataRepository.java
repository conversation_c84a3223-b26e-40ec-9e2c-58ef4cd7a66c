package com.nbcu.psi.repositories;
import java.util.List;
import com.nbcu.psi.model.Deal;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface DataRepository 
    extends JpaRepository<Deal, Long>{
    
    // The subquery limits the parent_deal_id to only those that have a budget record with sf_deal_id = null
    // The main query select all deals from those parent deals, so we can log the complete structure of parent deal.
    // We will loop through those deals/deal_links and skip those already exist in salesforce by using SF rest api.
    // (For deal_links, the sf_deal_id in budget can't be relied on, because some deals might have been added after sending to salesforce.)
    public static String queryFetchDealsThroughParent =
        " select " +
        " p.parent_deal_id, " +
        " p.parent_deal_name, " +
        "	dl.deal_link_id, " +
        "	dl.deal_link_name, " +
        "	dlt.single_opportunity, " +
        "	d.property_id, " +
        "	d.deal_id, " +
        "	d.deal_name, " +
        "	b.budget_id, " +
        "   pb.actual_prequarter_amount pb_actual_prequarter_amount, " +
        "   pb.actual_quarter4_amount pb_actual_quarter4_amount, " +
        "   pb.actual_quarter1_amount pb_actual_quarter1_amount, " +
        "   pb.actual_quarter2_amount pb_actual_quarter2_amount, " +
        "   pb.actual_quarter3_amount pb_actual_quarter3_amount, " +
        "   pb.actual_postquarter_amount pb_actual_postquarter_amount, " +
        "   b.actual_prequarter_amount, " +
        "   b.actual_quarter4_amount, " +
        "   b.actual_quarter1_amount, " +
        "   b.actual_quarter2_amount, " +
        "   b.actual_quarter3_amount, " +
        "   b.actual_postquarter_amount, " +
        "  ?3 stage, " +
        "	b.sf_deal_id, " +
        "	b.sf_opportunity_id, " +
        "	b.send_to_salesforce, " +
        "   b.sf_send_quarterly, " +
        "   b.sf_send_prior_year, " +
        "   b.sf_send_zero_dollars, " +
        "	sf_v.sfdc_vertical_id, " +
        "	sf_v.sfdc_vertical_name, " +
        "	sf_v.sfdc_property_id, " +
        "   d.placeholder " +
        " from {h-schema}budget b " +
        " join {h-schema}deal d on d.deal_id = b.deal_id " +
        " join {h-schema}budget_year cy on b.budget_year_id = cy.budget_year_id " +
        " join {h-schema}budget_year py on py.fall_year = cy.fall_year - 1 " +
        " left join {h-schema}budget pb on pb.budget_year_id = py.budget_year_id and pb.deal_id = d.deal_id " +
        " join {h-schema}parent_deal p on b.parent_deal_id = p.parent_deal_id and d.parent_deal_id = p.parent_deal_id  " +
        " left join {h-schema}deal_link_asc dla on dla.deal_id = d.deal_id " +
        " left join {h-schema}deal_link dl on dl.deal_link_id = dla.deal_link_id  " +
        " left join {h-schema}deal_link_type dlt on dlt.deal_link_type_id = dl.deal_link_type_id and dlt.send_to_salesforce = 1  " +
        " left join {h-schema}sfdc_intg_vertical sf_v ON d.sfdc_intg_vertical_id = sf_v.sfdc_intg_vertical_id " +
        " where b.parent_deal_id != -1 " +
        "   and p.parent_deal_id in ( " + 
        "     select distinct parent_deal_id  " + // parent may have multiple deals, as long as one of them has sf_deal_id = null, we will pick the parent
        "     from {h-schema}budget " + 
        "     where parent_deal_id != -1 " +
        "      and sf_deal_id is null " +
        "      and send_to_salesforce = 1 " +
        "      and budget_year_id = ?1 " +
        "   ) " +
        " 	and d.parent_deal_id != -1 " +
        " 	and b.send_to_salesforce = 1 " +
        "   and b.budget_year_id = ?1 " +
        // "   and b.deal_id = 134210 " +
        "   and b.updated_at > to_date(?2, 'YYYY-MM-DD HH24:MI:SS') ";
    @Query(nativeQuery = true, value = queryFetchDealsThroughParent)
    List<Object[]> fetchDealsThroughParent(int budgetYearId, String lastUpdatedDate, String stageName);

    // may need to display complete deals for a parent deal too
    // then we need to pick the deals has sf_sync = 1
    // currently just pick a deal for developing sending until pam_client change is ready
    public static String queryFetchSyncDealsThroughParent =
        " select " +
        " p.parent_deal_id, " +
        " p.parent_deal_name, " +
        "	dl.deal_link_id, " +
        "	dl.deal_link_name, " +
        "	dlt.single_opportunity, " +
        "	d.property_id, " +
        "	d.deal_id, " +
        "	d.deal_name, " +
        "	b.budget_id, " +
        "   pb.actual_prequarter_amount pb_actual_prequarter_amount, " +
        "   pb.actual_quarter4_amount pb_actual_quarter4_amount, " +
        "   pb.actual_quarter1_amount pb_actual_quarter1_amount, " +
        "   pb.actual_quarter2_amount pb_actual_quarter2_amount, " +
        "   pb.actual_quarter3_amount pb_actual_quarter3_amount, " +
        "   pb.actual_postquarter_amount pb_actual_postquarter_amount, " +
        "   b.actual_prequarter_amount, " +
        "   b.actual_quarter4_amount, " +
        "   b.actual_quarter1_amount, " +
        "   b.actual_quarter2_amount, " +
        "   b.actual_quarter3_amount, " +
        "   b.actual_postquarter_amount, " +
        "  ?3 stage, " +
        "	b.sf_deal_id, " +
        "	b.sf_opportunity_id, " +
        "	b.send_to_salesforce, " +
        "   b.sf_send_quarterly, " +
        "   0 sf_send_prior_year, " +
        "   b.sf_send_zero_dollars, " +
        "	sf_v.sfdc_vertical_id, " +
        "	sf_v.sfdc_vertical_name, " +
        "	sf_v.sfdc_property_id, " +
        "   d.placeholder " +
        " from {h-schema}budget b " +
        " join {h-schema}deal d on d.deal_id = b.deal_id " +
        " join {h-schema}budget_year cy on b.budget_year_id = cy.budget_year_id " +
        " join {h-schema}budget_year py on py.fall_year = cy.fall_year - 1 " +
        " left join {h-schema}budget pb on pb.budget_year_id = py.budget_year_id and pb.deal_id = d.deal_id " +
        " join {h-schema}parent_deal p on b.parent_deal_id = p.parent_deal_id and d.parent_deal_id = p.parent_deal_id  " +
        " left join {h-schema}deal_link_asc dla on dla.deal_id = d.deal_id " +
        " left join {h-schema}deal_link dl on dl.deal_link_id = dla.deal_link_id  " +
        " left join {h-schema}deal_link_type dlt on dlt.deal_link_type_id = dl.deal_link_type_id and dlt.send_to_salesforce = 1  " +
        " left join {h-schema}sfdc_intg_vertical sf_v ON d.sfdc_intg_vertical_id = sf_v.sfdc_intg_vertical_id " +
        " where b.parent_deal_id != -1 " +
        "   and p.parent_deal_id in ( " + 
        "     select distinct parent_deal_id  " + // parent may have multiple deals, as long as one of them has sf_deal_id = null, we will pick the parent
        "     from {h-schema}budget " + 
        "     where parent_deal_id != -1 " +
        "      and send_to_salesforce = 1 " +
        "      and budget_year_id = ?1 " +
        "   ) " +
        " 	and d.parent_deal_id != -1 " +
        // "   and b.deal_id = 100067 " +
        "   and b.budget_year_id = ?1 " +
        "   and b.sf_sync_at > to_date(?2, 'YYYY-MM-DD HH24:MI:SS')" +
        "   and b.sf_sync_status = 'pending' " +
        " 	and b.send_to_salesforce = 1 ";
    @Query(nativeQuery = true, value = queryFetchSyncDealsThroughParent)
    List<Object[]> fetchSyncDealsThroughParent(int budgetYearId, String lastUpdatedDate, String stageName);

    /**
     * Given a year, quarter and property_id, return start end of the quarter.
     * The so called combined means quarter_date_override table table will take precedence over quarter_date table.
     */
    public static String queryFetchCombinedQuarterDates =
    " with qd as (" +
    " select qd.finance_quarter_id, qd.quarter_start_date, qd.quarter_end_date " +
    " from {h-schema}quarter q " +
    " left join {h-schema}finance_quarter fq on q.quarter_id = fq.quarter_id " +
    " left join {h-schema}quarter_date qd on qd.finance_quarter_id = fq.finance_quarter_id " +
    " where year = ?1 and quarter = ?2) " +
    " select coalesce(qo.quarter_start_date, qd.quarter_start_date) start_date, " +
    " coalesce(qo.quarter_end_date, qd.quarter_end_date) end_date "+
    " from qd " +
    " left join {h-schema}quarter_date_override qo ON qo.finance_quarter_id = qd.finance_quarter_id and qo.property_id = ?3 " +
    " where rownum = 1";
    @Query(nativeQuery = true, value = queryFetchCombinedQuarterDates)
    List<Object[]> fetchCombinedQuarterDates(int year, int quarter, int property_id);

    /**
     * Fetch the year that has the default_budget_year = 1
     */
    public static String queryFetchCurrentBudgetYear =
    " select budget_year_id, budget_year_name " + 
    " from {h-schema}budget_year " +
    " where default_budget_year = 1 ";
    @Query(nativeQuery = true, value = queryFetchCurrentBudgetYear)
    List<Object[]> fetchCurrentBudgetYear();
  
}


