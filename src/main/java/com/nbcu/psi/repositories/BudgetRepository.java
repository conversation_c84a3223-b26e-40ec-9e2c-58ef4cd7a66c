package com.nbcu.psi.repositories;
import java.util.List;

import com.nbcu.psi.model.Budget;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
public interface BudgetRepository 
    extends JpaRepository<Budget, Long>{

    public static String queryFindUpdatedBudgetIds =
      "  with exclude_deals as (" +
      "  select distinct deal_id " +
      "  from {h-schema}deal_link_asc " +
      "  where deal_link_Id in ( " +
      "      select deal_link_id " +
      "      from {h-schema}deal_link " +
      "      where deal_link_type_id in ( " +
      "          select deal_link_type_id " +
      "          from {h-schema}deal_link_type " +
      "          where send_to_salesforce = 1)))" +
      " select b.budget_id " +
      " from {h-schema}budget b " +
      " join {h-schema}deal d on b.deal_id = d.deal_id " +
      " where b.send_to_salesforce = 1 " +
      " and d.deal_id not in ( select deal_id from exclude_deals) " +
      " and (b.updated_at > to_date(?1, 'YYYY-MM-DD HH24:MI:SS') " +
      " or d.updated_at > to_date(?1, 'YYYY-MM-DD HH24:MI:SS')) ";
    
    @Query(nativeQuery = true, value = queryFindUpdatedBudgetIds)
    List<Long> findUpdatedBudgetIds(String dateStr);
}


