package com.nbcu.psi.repositories;
import com.nbcu.psi.model.BudgetYear;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
public interface BudgetYearRepository 
       extends JpaRepository<BudgetYear, Long>{

  public static String queryFetchCurrentBudgetYear =
  " select * " + 
  " from {h-schema}budget_year " +
  " where default_budget_year = 1 " +
  " and rownum = 1 ";
  @Query(nativeQuery = true, value = queryFetchCurrentBudgetYear)
  BudgetYear fetchCurrentBudgetYear();

}


