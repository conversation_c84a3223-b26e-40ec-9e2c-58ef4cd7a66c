package com.nbcu.psi.repositories;
import com.nbcu.psi.model.SystemKeyAsc;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface SystemKeyAscRepository 
       extends JpaRepository<SystemKeyAsc, Long>{

  // Only retrieve the latest modified record.
  // It is possible that duplicat record exist in database, ex. property_id = 11701
  public static String queryFindById =
    " select * from (" +
    " select * " +
    " from {h-schema}system_key_asc " +
    " where to_char(pam_key) = to_char(?1) " +
    " and external_system_id = 1 " +
    " and system_key_asc_type_id in (1,2) " +
    " order by updated_at desc) " +
    " where rownum = 1";

  public static String queryFindByAdvertiserId = 
    queryFindById + " and external_key_type_id = 1 ";

  public static String queryFindByAgencyId = 
    queryFindById + " and external_key_type_id = 2 ";

  public static String queryFindByPropertyId = 
    queryFindById + " and external_key_type_id = 4 ";


  @Query(nativeQuery = true, value = queryFindByAdvertiserId)
  SystemKeyAsc findByAdvertiserId(long advertiserId);
  
  @Query(nativeQuery = true, value = queryFindByAgencyId)
  SystemKeyAsc findByAgencyId(long agencyId);

  @Query(nativeQuery = true, value = queryFindByPropertyId)
  SystemKeyAsc findByPropertyId(long propertyId);
}


