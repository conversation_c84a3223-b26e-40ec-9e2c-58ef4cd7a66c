package com.nbcu.psi.repositories;
import java.util.List;
import com.nbcu.psi.model.Deal;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface DealRepository 
    extends JpaRepository<Deal, Long>{
    public static String queryFindDealsThroughParent =
    " select " +
    " p.parent_deal_id, " +
    " p.parent_deal_name, " +
    "	dl.deal_link_id, " +
    "	dl.deal_link_name, " +
    "	dlt.single_opportunity, " +
    "	d.property_id, " +
    "	d.deal_id, " +
    "	d.deal_name, " +
    "	b.budget_id, " +
    "	b.actual_prequarter_amount, " +
    "	b.actual_quarter4_amount, " +
    "	b.actual_quarter1_amount, " +
    "	b.actual_quarter2_amount, " +
    "	b.actual_quarter3_amount, " +
    "	b.actual_postquarter_amount, " +
    "	b.sf_deal_id, " +
    "	b.sf_opportunity_id, " +
    "	b.send_to_salesforce, " +
    "	sf_v.sfdc_vertical_id, " +
    "	sf_v.sfdc_vertical_name, " +
    "	sf_v.sfdc_property_id " +
    " from budget b " +
    " join deal d on d.deal_id = b.deal_id " +
    " join parent_deal p on b.parent_deal_id = p.parent_deal_id and d.parent_deal_id = p.parent_deal_id  " +
    " left join deal_link_asc dla on dla.deal_id = d.deal_id " +
    " left join deal_link dl on dl.deal_link_id = dla.deal_link_id  " +
    " left join deal_link_type dlt on dlt.deal_link_type_id = dl.deal_link_type_id and dlt.send_to_salesforce = 1  " +
    " left join sfdc_intg_vertical sf_v ON d.sfdc_intg_vertical_id = sf_v.sfdc_intg_vertical_id " +
    " where b.parent_deal_id != -1 " +
    " 	and d.parent_deal_id != -1 " +
    " 	and b.send_to_salesforce = 1 ";
    // add timestamp

    @Query(nativeQuery = true, value = queryFindDealsThroughParent)
    List<Object[]> findDealsThroughParent(String dateStr);
}


