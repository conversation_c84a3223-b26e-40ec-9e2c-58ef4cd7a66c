package com.nbcu.psi;

import com.nbcu.psi.model.*;
import com.nbcu.psi.service.*;
import com.nbcu.psi.repositories.*;
import com.nbcu.psi.sf.*;
import com.nbcu.psi.pam.PamApiClient;
import com.nbcu.psi.mail.notification.EngineeringErrorNotification;
import org.springframework.stereotype.Service;
import org.springframework.core.env.Environment;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SpecialEventSyncLogic {
    // Core services
    @NonNull private final SpecialEventDealDetailService dealDetailService;
    @NonNull private final SpecialEventDealService dealService;
    @NonNull private final SpecialEventService eventService;
    @NonNull private final SfClient sfClient;
    @NonNull private final SfSpecialEventJsonBuilder sfJsonBuilder;
    @NonNull private final Logger logger;
    @NonNull private final PamApiClient pamApiClient;
    @NonNull private final EngineeringErrorNotification engineeringErrorNotification;
    @NonNull private final Environment environment;
    
    // Repositories for loading related entities
    @NonNull private final AdvertiserRepository advertiserRepository;
    @NonNull private final AgencyRepository agencyRepository;
    @NonNull private final PropertyRepository propertyRepository;
    @NonNull private final DemographicRepository demographicRepository;
    @NonNull private final AppUserRepository appUserRepository;
    @NonNull private final MeasurementTypeRepository measurementTypeRepository;
    @NonNull private final CurrencyRepository currencyRepository;
    @NonNull private final RatingStreamRepository ratingStreamRepository;
    @NonNull private final SystemKeyAscService systemKeyAscService;
    @NonNull private final ProductCategoryRepository productCategoryRepository;
    @NonNull private final SpecialEventQuarterHandler quarterHandler;
    @NonNull private final MarketplaceRepository marketplaceRepository;

    // Cache maps to avoid repeated database lookups
    private Map<Long, String> eventDealIdMap = new HashMap<>();
    private Map<Long, SpecialEventContext> contextMap = new HashMap<>();
    
    // For storing each of the failed special event processing for email notification
    private ArrayList<ResultRecord> failures = new ArrayList<ResultRecord>();

    public void processPendingDetails(String lastSyncDate) throws Exception {
        List<SpecialEventDealDetail> details = dealDetailService.findByStatusAndLastSyncDate("pending", lastSyncDate);
        Tool.pr("Found " + details.size() + " pending special event deal details to sync since " + lastSyncDate);

        try {
            // Group details by event ID and deal ID
            Map<Long, Map<Long, List<SpecialEventDealDetail>>> groupedDetails = details.stream()
                .collect(Collectors.groupingBy(
                    SpecialEventDealDetail::getSpecialEventId,
                    Collectors.groupingBy(SpecialEventDealDetail::getSpecialEventDealId)
                ));

            // Process each event
            for (Map.Entry<Long, Map<Long, List<SpecialEventDealDetail>>> eventEntry : groupedDetails.entrySet()) {
                long eventId = eventEntry.getKey();
                SpecialEvent event = eventService.findById(eventId);
                Tool.pr("Processing event: " + event.getSpecialEventName());

                // Process each deal within the event
                for (Map.Entry<Long, List<SpecialEventDealDetail>> dealEntry : eventEntry.getValue().entrySet()) {
                    long dealId = dealEntry.getKey();
                    List<SpecialEventDealDetail> dealDetails = dealEntry.getValue();
                    SpecialEventDeal deal = dealService.findById(dealId);
                    Tool.pr("Processing deal: " + deal.getSpecialEventDealId());

                    try {
                        // Setup log folder for the deal
                        String logFolder = Tool.getTimestamp() + "_special_event_deal_" + eventId + "_" + dealId;
                        logger.setLogFolder(logFolder);
                        logger.info(Tool.f("---Processing Special Event--- \n%s", Tool.formatModel(event)));
                        logger.info(Tool.f("Processing deal: %s", Tool.formatModel(deal)));

                        // Create context for this deal
                        SpecialEventContext context = loadSpecialEventContext(event, deal);
                        contextMap.put(dealId, context);
                        
                        // Set context on builder and create Salesforce deal if needed
                        sfJsonBuilder.setContext(context);
                        boolean dealCreationFailed = false;
                        try {
                            String sfDealId = checkOrCreateSalesforceDeal(context);
                            context.setSfDealId(sfDealId);
                            eventDealIdMap.put(dealId, sfDealId);

                            // Process all details for this deal
                            for (SpecialEventDealDetail detail : dealDetails) {
                                try {
                                    // Add divider before processing each detail
                                    logger.info("\n" + "=".repeat(80) + "\n");
                                    Tool.pr("Processing detail: " + detail.getSpecialEventDealDetailId());
                                    logger.info(Tool.f("Processing detail: %s", Tool.formatModel(detail)));

                                    sfJsonBuilder.setContext(context);
                                    processDealDetail(detail, context);

                                    // Update PAM API with success status
                                    pamApiClient.updateSpecialEventDetail(detail.getSpecialEventDealDetailId(), context.getSfDealId(), context.getSfOpportunityId(), "success");

                                } catch (Exception e) {
                                    // Add to error email with more context
                                    String headerStr = Tool.f("Failed to process Special Event Detail(%d) for Event(%s) Deal(%s): %s\n", 
                                        detail.getSpecialEventDealDetailId(), 
                                        event.getSpecialEventName(), 
                                        deal.getSpecialEventDealId(), 
                                        e.getMessage());
                                    
                                    // Add more context to the error log
                                    String detailErrorLog = Tool.f("%s\n\nEvent Context: %s\nDeal Context: %s\nDetail Context: %s", 
                                        Tool.getExceptionStr(e),
                                        Tool.formatModel(event),
                                        Tool.formatModel(deal),
                                        Tool.formatModel(detail));
                                    
                                    failures.add(new ResultRecord(headerStr, detailErrorLog));

                                    // Update PAM API with failure status
                                    pamApiClient.updateSpecialEventDetail(detail.getSpecialEventDealDetailId(), null, null, "failure");

                                    if (e instanceof SfApiException || e instanceof SfOtherException) {
                                        // If it is sf exception, continue with next detail
                                        continue;
                                    } else {
                                        throw e;
                                    }
                                }
                            }
                        } catch (SfApiException sfe) {
                            // Add to error email with more context
                            String headerStr = Tool.f("Failed to create/check Special Event Deal(%d) for Event(%s): %s\n", 
                                dealId, event.getSpecialEventName(), sfe.getMessage());
                            
                            failures.add(new ResultRecord(headerStr, logger.getErrorLog()));
                            dealCreationFailed = true;
                        }

                        // Write summary for this deal
                        logger.writeSummary();
                        logger.reset();

                        // If deal creation failed, throw the exception after writing summary
                        if (dealCreationFailed) {
                            throw new SfApiException("Failed to create/check Special Event Deal");
                        }
                    } catch (Exception e) {
                        // Add to error email only if it's not already captured at detail level
                        if (!(e instanceof SfApiException) && !(e instanceof SfOtherException)) {
                            String headerStr = Tool.f("Failed to process Special Event Deal(%d) for Event(%s): %s\n", 
                                dealId, event.getSpecialEventName(), e.getMessage());
                            failures.add(new ResultRecord(headerStr, logger.getErrorLog()));
                        }

                        if (e instanceof SfApiException || e instanceof SfOtherException) {
                            // If it is sf exception, continue with next deal
                            continue;
                        } else {
                            throw e;
                        }
                    }
                }
            }
        } finally {
            // Not matter if there is systematic error, accumulated sf exceptions will be sent out as single email.
            if (failures.size() > 0) {
                processSfException(failures);
            }
        }
    }

    private SpecialEventContext loadSpecialEventContext(SpecialEvent event, SpecialEventDeal deal) throws Exception {
        SpecialEventContext context = new SpecialEventContext();
        context.setSpecialEvent(event);
        context.setEventDeal(deal);
        // context.setDealIdWithYear(deal.getSpecialEventDealId() + "_" + event.getBudgetYear());

        // Load all related entities
        context.setAdvertiser(advertiserRepository.findById(deal.getAdvertiserId())
                .orElseThrow(() -> new Exception("Advertiser not found: " + deal.getAdvertiserId())));
        context.setAgency(agencyRepository.findById(deal.getAgencyId())
                .orElseThrow(() -> new Exception("Agency not found: " + deal.getAgencyId())));
        context.setDemographic(demographicRepository.findById(deal.getDemographicId())
                .orElseThrow(() -> new Exception("Demographic not found: " + deal.getDemographicId())));
        
        // Load the product category
        if (deal.getProductCategoryId() > 0) {
            context.setProductCategory(productCategoryRepository.findById(deal.getProductCategoryId())
                .orElseThrow(() -> new Exception("Product Category not found: " + deal.getProductCategoryId())));
        }
        
        // Load users
        context.setBuyingAppUser(appUserRepository.findById(deal.getBuyingAppUserId())
                .orElseThrow(() -> new Exception("Buying App User not found: " + deal.getBuyingAppUserId())));
        context.setClientAppUser(appUserRepository.findById(deal.getClientAppUserId())
                .orElseThrow(() -> new Exception("Client App User not found: " + deal.getClientAppUserId())));
                
        if (deal.getPlanningAppUserId() > 0) {
            context.setPlanningAppUser(appUserRepository.findById(deal.getPlanningAppUserId())
                    .orElse(null));
        }

        // Load rating streams
        if (deal.getRatingStreamId() > 0) {
            context.setDealRatingStream(ratingStreamRepository.findById(deal.getRatingStreamId())
                .orElse(null));
        }
        context.setDefaultRatingStream(ratingStreamRepository.findById(event.getDefaultRatingStreamId())
                .orElseThrow(() -> new Exception("Default Rating Stream not found: " + event.getDefaultRatingStreamId())));

        // Load measurement type and currency from event defaults
        context.setMeasurementType(measurementTypeRepository.findById(event.getDefaultMeasurementTypeId())
                .orElseThrow(() -> new Exception("Measurement Type not found: " + event.getDefaultMeasurementTypeId())));
        context.setCurrency(currencyRepository.findById(event.getDefaultCurrencyId())
                .orElseThrow(() -> new Exception("Currency not found: " + event.getDefaultCurrencyId())));

        // Load Salesforce IDs
        context.setSfAdvertiserId(systemKeyAscService.findByAdvertiserId(deal.getAdvertiserId()).getExternalSystemKey());
        context.setSfAgencyId(systemKeyAscService.findByAgencyId(deal.getAgencyId()).getExternalSystemKey());

        // Set stage and other metadata
        context.setStage(environment.getProperty("app.stagename"));
        context.setDealName(context.buildDealName());

        // Add marketplace loading if ID exists
        if (deal.getMarketplaceId() > 0) {
            context.setMarketplace(marketplaceRepository.findById(deal.getMarketplaceId())
                    .orElse(null));
        }

        // Add planner app user loading
        if (deal.getLinearPlannerAppUserId() > 0) {
            context.setPlannerAppUser(appUserRepository.findById(deal.getLinearPlannerAppUserId())
                    .orElse(null));
        }

        // Add linear planner app user loading
        if (deal.getLinearPlannerAppUserId() > 0) {
            context.setLinearPlannerAppUser(appUserRepository.findById(deal.getLinearPlannerAppUserId())
                    .orElse(null));
        }

        // Add digital planner app user loading
        if (deal.getDigitalPlannerAppUserId() > 0) {
            context.setDigitalPlannerAppUser(appUserRepository.findById(deal.getDigitalPlannerAppUserId())
                    .orElse(null));
        }

        // Add account manager app user loading
        if (deal.getAccountManagerAppUserId() > 0) {
            context.setAccountManagerAppUser(appUserRepository.findById(deal.getAccountManagerAppUserId())
                    .orElse(null));
        }

        return context;
    }

    private String checkOrCreateSalesforceDeal(SpecialEventContext context) throws Exception {
        String fname = "special_event_deal_" + context.getEventDeal().getSpecialEventDealId();
        try {
            String sfParentDealId = context.buildSpecialEventDealId();
            String soql = String.format(
                "SELECT Id FROM Deal__c WHERE PAM_Parent_Deal_ID__c = '%s'",
                sfParentDealId
            );
            String sfDealId = sfClient.fetchSFIdBySOQL(soql);
            if (sfDealId != null) {
                logger.info(Tool.f("Special Event Deal (%s) already exists in SF as deal (%s). Skip insert.",
                    sfParentDealId, sfDealId));
            } else {
                String requestJson = sfJsonBuilder.buildSpecialEventDeal();
                String responseJson = sfClient.sendComposite(requestJson);
                sfDealId = sfClient.extractObjectId(responseJson, "Deal__c");
                logger.successPayload(fname);
                logger.debug(Tool.f("Successfully created SF deal (%s) for Special Event deal (%d)",
                    sfDealId, context.getEventDeal().getSpecialEventDealId()));
            }
            return sfDealId;
        } catch (SfApiException sfe) {
            logger.failurePayload(fname, sfe);
            throw sfe;
        } catch (Exception ex) {
            logger.exceptionPayload(fname, ex);
            throw ex;
        }
    }

    private void processDealDetail(SpecialEventDealDetail detail, SpecialEventContext context) throws Exception {
        // Base filename without sync prefix
        String fname = "special_event_detail_" + detail.getSpecialEventDealDetailId();
        try {
            // Load property for this detail
            Property property = propertyRepository.findById(detail.getPropertyId())
                    .orElseThrow(() -> new Exception("Property not found: " + detail.getPropertyId()));
            context.setProperty(property);
            
            // Set the selling vertical from the property
            context.setSellingVertical(property.getSellingVertical());
            
            // Load property's Salesforce ID
            String sfPropertyId = systemKeyAscService.findByPropertyId(detail.getPropertyId()).getExternalSystemKey();
            context.setSfPropertyId(sfPropertyId);
            context.setDigital(context.isDigitalProperty());

            // Set planner app user based on property type
            if (context.isDigitalProperty()) {
                context.setPlannerAppUser(context.getDigitalPlannerAppUser());
            } else {
                context.setPlannerAppUser(context.getLinearPlannerAppUser());
            }

            quarterHandler.initializeForEvent(context.getSpecialEvent());

            String responseJson = "";
            String sfOpportunityId = detail.getSfOpportunityId();
            
            // If no existing opportunity ID, check if one exists in Salesforce
            if (sfOpportunityId == null) {
                String seIdWithSeDetailId = context.buildSpecialEventDetailId(detail);
                String soql = String.format(
                    "SELECT Id FROM Opportunity WHERE PAM_Deal_ID__c = '%s'",
                    seIdWithSeDetailId
                );
                sfOpportunityId = sfClient.fetchSFIdBySOQL(soql);
                if (sfOpportunityId != null) {
                    logger.info(Tool.f("Special Event Detail (%s) already exists in SF as opportunity (%s). Will update.",
                        seIdWithSeDetailId, sfOpportunityId));
                }
                detail.setSfOpportunityId(sfOpportunityId);
            }

            if (sfOpportunityId != null) {
                logger.debug("Updating existing opportunity: " + sfOpportunityId);
                String requestJson = sfJsonBuilder.buildSyncOpportunityJson(detail);
                responseJson = sfClient.sendComposite(requestJson);
                // Use sync_ prefix for updates
                fname = "sync_" + fname;
            } else {
                logger.debug("Creating new opportunity");
                String requestJson = sfJsonBuilder.buildOpportunityJson(detail);
                responseJson = sfClient.sendComposite(requestJson);
                sfOpportunityId = sfClient.extractObjectId(responseJson, "Opportunity");
                context.setSfOpportunityId(sfOpportunityId);
                logger.debug(Tool.f("Created new opportunity: %s", sfOpportunityId));
                // Use base filename for new opportunities
            }

            // Store success payload
            logger.successPayload(fname);

        } catch (SfApiException sfe) {
            logger.failurePayload(fname, sfe);
            throw sfe;
        } catch (Exception ex) {
            logger.exceptionPayload(fname, ex);
            throw ex;
        }
    }

    private void processSfException(ArrayList<ResultRecord> failures) {
        String engineerStr = "";
        String divider = "\n" + "=".repeat(80) + "\n";

        for (int i = 0; i < failures.size(); i++) {
            ResultRecord r = failures.get(i);
            engineerStr += r.headerStr;
            engineerStr += String.format("\n%s", r.errorLog);
            // Add divider between errors, but not after the last one
            if (i < failures.size() - 1) {
                engineerStr += divider;
            }
        }

        engineerStr = String.format("<h2>PSI: Special Event Salesforce Processing Error</h2><pre style='font-family: monospace;'>%s</pre>", engineerStr);

        Tool.pr("sending engineering SalesForce exceptions:\n" + engineerStr);
        engineeringErrorNotification.send(engineerStr);
    }
}