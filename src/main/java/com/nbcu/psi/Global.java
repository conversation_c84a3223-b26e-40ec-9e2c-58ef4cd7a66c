package com.nbcu.psi;

import org.springframework.stereotype.Service;
import com.nbcu.psi.sf.*;
import com.nbcu.psi.service.*;
import com.nbcu.psi.model.*;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;

@Getter
@Setter
@Service
@RequiredArgsConstructor
public class Global {
    @NonNull
    private BudgetService budgetService;
    @NonNull
    private BudgetYearService budgetYearService;
    @NonNull
    private SystemKeyAscService systemKeyAscService;
    @NonNull
    private QuarterDateCalculator quarterDateCalculator;
    @NonNull
    private SfClient sfClient;
    @NonNull
    private PamUtils pamUtils;

    // Core domain models
    public Budget budget = null;
    public Deal deal = null;
    public DealLink dealLink = null;
    public Agency agency = null;
    public Advertiser advertiser = null;
    public AdvertiserBrand advertiserBrand = null;
    public Property property = null;
    public PropertyType propertyType = null;
    public SellingVertical sellingVertical = null;
    public Demographic demographic = null;
    public Marketplace marketplace = null;
    public BudgetYear budgetYear = null;
    public RatingStream ratingStream = null;
    public MeasurementType measurementType = null;
    public Currency currency = null;

    // Names and IDs from domain models
    public String marketplaceName = null;
    public String measurementTypeName = null;
    public String currencyName = null;
    public String advertiserName = null;
    public String brandName = null;
    public String propertyName = null;
    public String propertyTypeName = null;
    public String sellingVerticalName = null;
    public String demographicName = null;
    public String ratingStreamName = null;

    // Flags
    public boolean isPeacock = false;
    public boolean isAdsmart = false;
    public boolean isPlaceholder = false;
    public boolean isUpfront = false;
    public boolean local = false;
    public boolean convertFirstQuarter = false;

    // Salesforce IDs
    public String sfAdvertiser = null;
    public String sfAgency = null;
    public String sfProperty = null;
    public String sfDealId = null;

    // Additional properties
    public Boolean isDigital = null;
    public String buyingAeSsoId = null;
    public String clientAeSsoId = null;
    public String plannerSsoId = null;
    public String accountManagerSsoId = null;

    // Budget year related
    public String budgetYearName = null;
    public String dealYear = null;  // 2025 for 24/25
    public String season = null;    // 24/25
    public String yearSeason = null;
    public String py = null;
    public String cy = null;
    public String tz = "UTC";
    public String[] quarterNames = new String[6];

    // IDs and amounts
    public int budgetYearId = -1;
    public long dealId = -1;
    public long dealLinkId = -1;
    public long budgetId = -1;
    public long propertyId = -1;
    public long parentDealId = -1;
    public String parentDealName = null;
    public ParentDealRecord parentDealRecord = null;
    public int quarterAmounts[];
    
    // Deal related
    public String dealName = null;
    public String dealNameTpl = null;
    public String[] sfOppIdsMask;
    private String verticalName;
    
    public void setCurrentBudgetYear(BudgetYear budgetYear) {
        this.budgetYearName = budgetYear.getBudgetYearName();
        this.budgetYearId = (int)budgetYear.getBudgetYearId();
        this.dealYear = this.budgetYearName.substring(5, 9);
        this.py = this.budgetYearName.substring(2, 4);
        this.cy = this.budgetYearName.substring(7, 9);
        this.season = py + "/" + cy;
        initializeQuarterNames();
    }

    private void initializeQuarterNames() {
        this.quarterNames[0] = "3Q" + py;
        this.quarterNames[1] = "4Q" + py;
        this.quarterNames[2] = "1Q" + cy;
        this.quarterNames[3] = "2Q" + cy;
        this.quarterNames[4] = "3Q" + cy;
        this.quarterNames[5] = "4Q" + cy;
    }

    public String withYear(Object id) {
        return "" + id + "-" + dealYear;
    }

    public String getDealIdWithYear() {
        return withYear(dealId);
    }

    public String getParentDealIdWithYear() {
        return withYear(parentDealId);
    }

    public void setDealRecord(DealRecord dealRecord) throws Exception {
        this.dealId = dealRecord.getDealId();
        this.budgetId = dealRecord.getBudgetId();
        this.budget = budgetService.findById(budgetId);
        deriveValues();
    }

    public void setDealLinkRecord(DealLinkRecord dealLinkRecord) throws Exception {
        this.dealLinkId = dealLinkRecord.getDealLinkId();
        this.budgetId = dealLinkRecord.getBudgetId();
        this.budget = budgetService.findById(budgetId);
        deriveValues();
    }

    public void setParentDealRecord(ParentDealRecord parentDealRecord) throws Exception {
        this.parentDealRecord = parentDealRecord;
        this.parentDealId = parentDealRecord.getParentDealId();
        this.budget = budgetService.findById(parentDealRecord.getFirstBudgetId());
        deriveValues();
    }

    public void deriveValues() throws Exception {
        // Set core entities
        this.budgetId = budget.getBudgetId();
        this.deal = budget.getDeal();
        this.agency = deal.getAgency();
        this.advertiser = deal.getAdvertiser();
        this.advertiserBrand = deal.getAdvertiserBrand();
        this.property = deal.getProperty();
        this.propertyType = property.getPropertyType();
        this.sellingVertical = property.getSellingVertical();
        this.demographic = deal.getDemographic();
        this.marketplace = deal.getMarketplace();
        this.budgetYear = budget.getBudgetYear();
        this.ratingStream = deal.getRatingStream();
        this.measurementType = budget.getMeasurementType();
        this.currency = budget.getCurrency();
        this.propertyId = property.getPropertyId();

        // PAM-813 Add Vertical to SF Opp name (not selling vertical)
        this.verticalName = deal.getVertical().getVerticalName();

        // Set names
        this.advertiserName = advertiser.getAdvertiserName();
        if (advertiserBrand != null) {
            this.brandName = advertiserBrand.getAdvertiserBrandName();
        }
        this.propertyName = property.getPropertyName();

        // Use PamUtils for conversions
        this.propertyTypeName = pamUtils.convertPropertyType(property);
        this.sellingVerticalName = pamUtils.convertSellingVerticalName(sellingVertical.getSellingVerticalName());
        this.demographicName = demographic.getDemographicName();
        this.ratingStreamName = pamUtils.convertRatingStreamName(ratingStream.getRatingStreamName());
        this.marketplaceName = pamUtils.convertMarketplaceName(marketplace.getMarketplaceName());

        // Get Salesforce IDs
        this.sfAdvertiser = pamUtils.getAdvertiserSfId(advertiser);
        this.sfAgency = pamUtils.getAgencySfId(agency);
        this.sfProperty = pamUtils.getPropertySfId(property);

        // Set additional properties
        this.isDigital = "Digital".equals(propertyType.getPropertyTypeName());
        this.buyingAeSsoId = deal.getAppUser2().getSsoId().toString();
        this.clientAeSsoId = deal.getAppUser4().getSsoId().toString();
        this.plannerSsoId = deal.getAppUser3().getSsoId().toString();
        this.accountManagerSsoId = deal.getAccountManagerAppUser() != null ? deal.getAccountManagerAppUser().getSsoId().toString() : null;

        // Handle measurement type
        this.measurementTypeName = deriveMeasurementTypeName();

        // Handle currency
        this.currencyName = deriveCurrencyName();

        // Set flags
        this.isPeacock = "Peacock".equals(propertyName) || "Peacock AX".equals(propertyName);
        this.isAdsmart = "Optimized Linear - Advanced".equals(propertyName) ||
                        "Optimized Linear - Demo".equals(propertyName);
        this.isPlaceholder = Tool.bigDecimalToBoolean(deal.getPlaceholder());
        this.isUpfront = "Upfront".equals(marketplace.getMarketplaceName());

        // Set year season
        this.yearSeason = getDealYear();
        if (isUpfront) {
            this.yearSeason = getSeason();
        }
        this.dealName = getDealName();
    }

    private String deriveMeasurementTypeName() {
        if (measurementType != null && measurementType.getMeasurementTypeId() != -1) {
            return measurementType.getMeasurementTypeName();
        }
        return property.getMeasurementType().getMeasurementTypeName();
    }

    private String deriveCurrencyName() {
        if (currency != null && currency.getCurrencyId() != -1) {
            return currency.getCurrencyName();
        }
        return "Nielsen";
    }

    public String calculateParentDealName() {
        String yearSeason = getDealYear();
        if (isUpfront) {
            yearSeason = getSeason();
        }
        return parentDealRecord.getParentDealName() + " " + yearSeason;
    }

    public String calculateParentDealId() {
        return withYear(parentDealId);
    }

    public String calculateDealId() {
        return withYear(dealId);
    }

    public String calculateDealLinkId() {
        return withYear(dealLinkId);
    }

    public String calculateDealName(String quarterStr) {
        String dealName = parentDealName;
        if (isPeacock) {
            dealName = propertyName;
        }
        if (brandName != null) {
            dealName = brandName + "_" + dealName;
        }
        if (isPeacock) {
            dealName = advertiserName + "_" + dealName + " UF";
        }
        dealName += "_" + yearSeason;
        if (isDigital || isAdsmart) {
            if (quarterStr != null && !quarterStr.isEmpty()) {
                dealName += "_" + quarterStr;
            }
        }
        if (!isPeacock) {
            dealName += "_@{product2_ref(${sf_property}).records[0].Line_Of_Busines__c}";
            dealName += " - @{product2_ref(${sf_property}).records[0].Name}";
        }

        if (verticalName != null && !verticalName.trim().isEmpty()) {
            dealName = verticalName + "_" + dealName;
        }
        return Tool.toOneLine(dealName);
    }

    public String calculateDealName() {
        return calculateDealName(null);
    }
}