package com.nbcu.psi;

import java.util.HashMap;

/**
 * BackupHashMap is a HashMap that allows you to backup and restore the state of the map.
 */
public class BackupHashMap<K, V> extends HashMap<K, V> {
    private HashMap<K, V> backupMap;

    public BackupHashMap() {
        super();
        backupMap = new HashMap<>();
    }

    // Backup the current state of the map
    public void backup() {
        backupMap.clear();
        backupMap.putAll(this);
    }

    // Restore the map to the last backed up state
    public void restore() {
        this.clear();
        this.putAll(backupMap);
    }

    // check if a backup exists
    public boolean hasBackup() {
        return !backupMap.isEmpty();
    }
}

