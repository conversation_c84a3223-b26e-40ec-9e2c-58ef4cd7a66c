package com.nbcu.psi;
import com.nbcu.psi.Tool;
import java.math.BigDecimal;
import java.util.*;
import org.springframework.stereotype.Service;
import com.nbcu.psi.service.*;
import com.nbcu.psi.model.*;
import com.nbcu.psi.s3.S3Util;
import org.springframework.core.env.Environment;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import com.nbcu.psi.sf.SfClient;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * Logger allows to log info and error messages, and dump payloads to S3
 * There are two buffers, infoSb will goto summary file and errorSb will goto email errors.
 * info method will log to both buffers
 * debug method will log to info buffer only.
 * successPayload method will save payload to S3 and log S3 link to info buffer only.
 * failurePayload method will save payload to S3 and log S3 link to both info and error buffer.
 * exceptionPayload differs from failurePayload in that it will log exception message as payload.
 */
@Service
@RequiredArgsConstructor
public class Logger {

  @NonNull
  private S3Util s3Util;
  
  @NonNull
  private SfClient sfClient;

  @NonNull
  private Environment env;

  public String logFolder = "";
  public StringBuffer infoSb = new StringBuffer();
  public StringBuffer errorSb = new StringBuffer();

  /**
   * ParentDeal has multiple payloads, so need to create a folder
   */
  public void setLogFolder(String logFolder) {
    this.logFolder = logFolder;
  }

  public void reset() {
    this.logFolder = "";
    infoSb = new StringBuffer();
    errorSb = new StringBuffer();
  }

  public String logPayload(String fname, String payload) {
    String fpath = null;
    try {
      String folder = getDumpFolder();
      if (!"".equals(logFolder)) {
        folder += "/" + logFolder + "/";
      }
      fpath = folder + fname;
      s3Util.writeStr(fpath, payload);
    } catch (Exception e) {
      Tool.pr("Can't dump payload:" + fname + ":" + Tool.getExceptionStr(e));
    } finally {
      return s3Util.getFullPath(fpath);
    }
  }

  /**
   * Write summary file and reset the buffer.
   */
  public void writeSummary() {
    logPayload("summary", infoSb.toString());
  }

  public String getErrorLog() {
    String str = errorSb.toString();
    return str;
  }

  /**
   * debug will only be logged to summary.
   * @param msg
   */
  public void debug(String msg) {
    Tool.p(msg);
    infoSb.append(msg + "\n");
  }

  /**
   * info will be logged to both summary and error email
   */
  public void info(String msg) {
    Tool.p(msg);
    infoSb.append(msg + "\n");
    errorSb.append(msg + "\n");
  }

  public String successPayload(String fname) {
    String logFile = logPayload("success_" + fname, sfClient.getAndResetPayloadLog());
    debug(formatBashShowPayload(logFile));
    return logFile;
  }

  public String failurePayload(String fname, Exception sfe) {
    String logFile = logPayload("failure_" + fname, sfClient.getAndResetPayloadLog());
    info(formatExceptionWithPayload(logFile, sfe));
    return logFile;
  }

  public String exceptionPayload(String fname, Exception ex) throws Exception {
    String payloadWithException = sfClient.getAndResetPayloadLog() + "\n\n\n\n" + Tool.getExceptionStr(ex);
    String logFile = logPayload("exception_" + fname, payloadWithException);
    info(formatExceptionWithPayload(logFile, ex));
    return logFile;
  }

  public String formatExceptionWithPayload(String logFile, Exception e) {
    StringBuffer sb = new StringBuffer();
    sb.append(Tool.f("Error occurred: %s\n", Tool.getExceptionStr(e)));
    sb.append(formatBashShowPayload(logFile));
    return sb.toString();
  }

  public String formatBashShowPayload(String logFile) {
    return Tool.f("See Payload/Response\n<br /> aws s3 cp %s  - && echo \"\"<hr /><br />", logFile);
  }

  public void createDumpFolder() throws Exception {
    Files.createDirectories(Paths.get(getDumpFolder()));
  }

  public String getDumpFolder() {
    return env.getProperty("s3.dump-folder");
  }
}