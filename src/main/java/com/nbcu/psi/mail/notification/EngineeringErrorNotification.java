package com.nbcu.psi.mail.notification;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import com.nbcu.psi.mail.MailerService;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
@Service
@RequiredArgsConstructor
public class EngineeringErrorNotification implements NotificationService {
  String errorMessage;

  @Value("${email.engineering-error.recipients}")    
  String[] recipients;
  String bodyStr = "";

  @NonNull
  private Environment environment;

  @NonNull
  private MailerService mailerService;

  public void send(String bodyStr){
    this.bodyStr = bodyStr;
    send();
  }

  public String[] recipients() {
    return this.recipients;
  }

  public String subject() {
    return String.format("%s - %s", this.environment.getProperty("email.engineering-error.subject"), this.environment.getActiveProfiles()[0]);
  }

  public String body() {
    return this.bodyStr;
  }

  public MailerService mailerService() {
    return this.mailerService;
  }
}
