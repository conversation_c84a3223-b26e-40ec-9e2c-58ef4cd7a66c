package com.nbcu.psi.mail;

import com.amazonaws.regions.Regions;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailService;
import com.amazonaws.services.simpleemail.AmazonSimpleEmailServiceClientBuilder;
import com.amazonaws.services.simpleemail.model.Body;
import com.amazonaws.services.simpleemail.model.Content;
import com.amazonaws.services.simpleemail.model.Destination;
import com.amazonaws.services.simpleemail.model.Message;
import com.amazonaws.services.simpleemail.model.SendEmailRequest;
import com.nbcu.psi.Tool;
import com.nbcu.psi.Global;

import org.springframework.stereotype.Service;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Service
@RequiredArgsConstructor
public class MailerService {
  final static String FROM_EMAIL = "<EMAIL>";

  @NonNull
  private Global global;

  public void send(String[] toAddresses, String subject, String body) {
    if (global.isLocal()) {
      sendLocal(toAddresses, subject, body);
    } else {
      sendSES(toAddresses, subject, body);
    }
  }

  private void sendLocal(String[] toAddresses, String subject, String body) {
    try {
      String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
      String emailContent = String.format(
        "To: %s\nSubject: %s\nFrom: %s\n\n%s",
        String.join(", ", toAddresses),
        subject,
        FROM_EMAIL,
        body
      );
      
      // Create tmp/email directory if it doesn't exist
      Files.createDirectories(Paths.get("tmp/email"));
      
      // Write to file
      String filename = String.format("tmp/email/%s_email.txt", timestamp);
      Files.writeString(Paths.get(filename), emailContent);
      
      Tool.p("Email logged to file: " + filename);
    } catch (Exception ex) {
      Tool.p("Failed to log email to file. Error message: " + ex.getMessage());
    }
  }

  private void sendSES(String[] toAddresses, String subject, String body) {
    try {
      AmazonSimpleEmailService client = 
          AmazonSimpleEmailServiceClientBuilder.standard()
            .withRegion(Regions.US_EAST_1).build();
      SendEmailRequest request = new SendEmailRequest()
          .withDestination(
              new Destination().withToAddresses(toAddresses))
          .withMessage(new Message()
              .withBody(new Body()
                  .withHtml(new Content()
                      .withCharset("UTF-8").withData(body)))
              .withSubject(new Content()
                  .withCharset("UTF-8").withData(subject)))
          .withSource(FROM_EMAIL);

      client.sendEmail(request);
      Tool.p("Email sent!");
    } catch (Exception ex) {
      Tool.p("The email was not sent. Error message: " + ex.getMessage());
    }
  }
}
