package com.nbcu.psi.pam;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.Entity;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nbcu.psi.Global;
import com.nbcu.psi.Tool;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

@Service
public class PamApiClient {
    private Client client;
    
    @Autowired
    private Global g;
    
    @Autowired
    Environment environment;

    public PamApiClient() {
        client = ClientBuilder.newClient();
    }

    public void updateSpecialEventDetail(long detailId, String sfDealId, String sfOpportunityId, String status) throws Exception {
      String url = pamApiUrl() + "/api/sf_callback/update_special_event_details/";
      Map<String, Object> payload = new HashMap<>();
      payload.put("smuser", this.environment.getProperty("pam.service-user-sso"));
      payload.put("special_event_details_ids", String.valueOf(detailId)); // Convert detailId to String and rename key
  
      if (sfDealId != null) {
          payload.put("sf_deal_id", sfDealId);
      }
      if (sfOpportunityId != null) {
          payload.put("sf_opportunity_id", sfOpportunityId);
      }
      if (status != null) {
          payload.put("sf_sync_status", status);
      }
  
      sfCallback(url, payload);
    }

    // Existing methods...
    public String healthCheck() throws Exception {
        String url = pamApiUrl() + "/health_check";
        try {
            Response res = client.target(url).request("text/plain").get();
            if (res.getStatus() == 200) {
                String output = res.readEntity(String.class);
                return output;
            }
            throw new Exception(Tool.f("status code (%s)", res.getStatus()));
        } catch (Exception e) {
            throw new Exception(Tool.f("PAM API Error: %s %s", url, e.getMessage()));
        }
    }

    public void updateBudgets(List<Integer> budgetIds, String sfDealId, String sfOpportunityId) throws Exception {
        String url = pamApiUrl() + "/api/sf_callback/update_budgets/";
        Map<String, Object> payload = new HashMap<>();
        payload.put("smuser", this.environment.getProperty("pam.service-user-sso"));
        payload.put("budget_ids", Tool.idListStr(budgetIds));
        payload.put("sf_opportunity_id", sfOpportunityId);
        payload.put("sf_deal_id", sfDealId);
        sfCallback(url, payload);
    }

    public void updateBudgetsSyncStatus(int budgetIds, String status) throws Exception {
        String url = pamApiUrl() + "/api/sf_callback/update_budget_sync_status/";
        Map<String, Object> payload = new HashMap<>();
        payload.put("smuser", this.environment.getProperty("pam.service-user-sso"));
        payload.put("budget_id", "" + budgetIds);
        payload.put("sf_sync_status", status);
        sfCallback(url, payload);
    }

    public void updateBudgetsSyncStatusByDealId(String dealIds, String status) throws Exception {
        String url = pamApiUrl() + "/api/sf_callback/update_budget_sync_status_by_deal_ids/";
        Map<String, Object> payload = new HashMap<>();
        payload.put("smuser", this.environment.getProperty("pam.service-user-sso"));
        payload.put("deal_ids", "" + dealIds);
        payload.put("sf_sync_status", status);
        sfCallback(url, payload);
    }

    public void updateParentDeal(int parentDealId, String sfDealId) throws Exception {
        String url = pamApiUrl() + "/api/sf_callback/update_parent_deal/";
        Map<String, Object> payload = new HashMap<>();
        payload.put("smuser", this.environment.getProperty("pam.service-user-sso"));
        payload.put("parent_deal_id", parentDealId);
        payload.put("sf_deal_id", sfDealId);
        sfCallback(url, payload);
    }

    public void sfCallback(String url, Map<String,Object> payload) throws Exception {
        String json = (new ObjectMapper()).writeValueAsString(payload);
        Entity<String> entity = Entity.json(json);
        String response = client.target(url)
                            .request(MediaType.APPLICATION_JSON_TYPE)
                            .put(entity, String.class);
    }

    private String pamApiUrl() {
        if (g.isLocal()) {
            return this.environment.getProperty("local-api-url");
        } else {
            return this.environment.getProperty("pam.api-url");
        }
    }
}