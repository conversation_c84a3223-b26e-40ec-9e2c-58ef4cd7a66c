package com.nbcu.psi.service;

import com.nbcu.psi.model.DealLinkType;
import com.nbcu.psi.repositories.DealLinkTypeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class DealLinkTypeServiceImpl
       implements DealLinkTypeService {
  @Autowired
  private DealLinkTypeRepository dealLinkTypeRepository;

  @Override
  public List<DealLinkType> loadAll() {
    return dealLinkTypeRepository.findAll();
  }
}
