package com.nbcu.psi.service;

import com.nbcu.psi.model.SpecialEventDeal;
import com.nbcu.psi.repositories.SpecialEventDealRepository;
import org.springframework.stereotype.Service;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class SpecialEventDealService {

    @NonNull
    private SpecialEventDealRepository repository;

    public SpecialEventDeal findById(long id) {
        return repository.findById(id).orElse(null);
    }
}