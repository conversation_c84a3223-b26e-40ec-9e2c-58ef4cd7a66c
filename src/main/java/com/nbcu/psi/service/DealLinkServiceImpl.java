package com.nbcu.psi.service;

import com.nbcu.psi.Tool;
import com.nbcu.psi.model.DealLink;
import com.nbcu.psi.model.DealLinkDaypartRecord;
import com.nbcu.psi.model.DealLinkRecord;
import com.nbcu.psi.repositories.DealLinkRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


@Service
public class DealLinkServiceImpl
       implements DealLinkService {
  @Autowired
  private DealLinkRepository dealLinkRepository;

  @Override
  public List<DealLink> loadAll() {
    return dealLinkRepository.findAll();
  }

  public List<DealLinkRecord> findInsertDealLinkRecords(String dateStr) {
    List<Object[]> dls = dealLinkRepository.findInsertDealLinks(dateStr);
    List<DealLinkRecord> records = new ArrayList<DealLinkRecord>();
    for (Object[] dl: dls) {
      int idx = 0;
      long dealLinkId = Tool.bigDecimalToLong(dl[idx++]);
      DealLinkRecord record = findOrCreateDealLinkRecord(records, dealLinkId);
      DealLinkDaypartRecord daypart = new DealLinkDaypartRecord();
      daypart.setDealLinkId(dealLinkId);
      daypart.setDealLinkName((String)dl[idx++]);
      daypart.setSingleOpportunity(Tool.bigDecimalToBoolean(dl[idx++]));
      daypart.setPropertyId(Tool.bigDecimalToLong(dl[idx++]));
      daypart.setBudgetId(Tool.bigDecimalToLong(dl[idx++]));
      daypart.setSfdcVerticalId((String)dl[idx++]);
      daypart.setSfdcVerticalName((String)dl[idx++]);
      daypart.setSfdcPropertyId((String)dl[idx++]);
      daypart.setActualPrequarterAmount(Tool.bigDecimalToLong(dl[idx++]));
      daypart.setActualQuarter4Amount(Tool.bigDecimalToLong(dl[idx++]));
      daypart.setActualQuarter1Amount(Tool.bigDecimalToLong(dl[idx++]));
      daypart.setActualQuarter2Amount(Tool.bigDecimalToLong(dl[idx++]));
      daypart.setActualQuarter3Amount(Tool.bigDecimalToLong(dl[idx++]));
      daypart.setActualPostquarterAmount(Tool.bigDecimalToLong(dl[idx++]));
      daypart.setSfDealId((String)dl[idx++]);
      daypart.setSfOpportunityId((String)dl[idx++]);
      record.add(daypart);
    }
    for (DealLinkRecord record: records) {
      record.aggregate();
    }
    return records;
  }

  public DealLinkRecord findOrCreateDealLinkRecord(List<DealLinkRecord> records, long dealLinkId) {
    for (DealLinkRecord record: records) {
      if (record.getDealLinkId() == dealLinkId) {
        return record;
      }
    }
    DealLinkRecord record = new DealLinkRecord();
    record.setDealLinkId(dealLinkId);
    records.add(record);
    return record;
  }
}
