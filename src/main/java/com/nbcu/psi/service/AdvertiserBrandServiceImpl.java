package com.nbcu.psi.service;

import com.nbcu.psi.model.Advertiser;
import com.nbcu.psi.model.AdvertiserBrand;
import com.nbcu.psi.repositories.AdvertiserBrandRepository;
import com.nbcu.psi.repositories.AdvertiserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class AdvertiserBrandServiceImpl
       implements AdvertiserBrandService {
  @Autowired
  private AdvertiserBrandRepository advertiserBrandRepository;

  @Override
  public List<AdvertiserBrand> loadAll() {
    return advertiserBrandRepository.findAll();
  }

  public List<AdvertiserBrand> findByName(String name) {
    return advertiserBrandRepository.findByAdvertiserBrandName(name);
  }
  
}
