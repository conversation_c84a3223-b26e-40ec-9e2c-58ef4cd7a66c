package com.nbcu.psi.service;

import com.nbcu.psi.model.MeasurementType;
import com.nbcu.psi.repositories.MeasurementTypeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class MeasurementTypeServiceImpl
       implements MeasurementTypeService {
  @Autowired
  private MeasurementTypeRepository measurementTypeRepository;

  @Override
  public List<MeasurementType> loadAll() {
    return measurementTypeRepository.findAll();
  }
}
