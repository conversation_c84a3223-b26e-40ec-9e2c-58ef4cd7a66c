package com.nbcu.psi.service;

import com.nbcu.psi.model.Marketplace;
import com.nbcu.psi.repositories.MarketplaceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class MarketplaceServiceImpl
       implements MarketplaceService {
  @Autowired
  private MarketplaceRepository marketplaceRepository;

  @Override
  public List<Marketplace> loadAll() {
    return marketplaceRepository.findAll();
  }
}
