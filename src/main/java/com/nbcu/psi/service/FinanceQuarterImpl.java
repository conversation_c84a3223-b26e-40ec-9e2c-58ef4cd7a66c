package com.nbcu.psi.service;

import com.nbcu.psi.model.FinanceQuarter;
import com.nbcu.psi.repositories.FinanceQuarterRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class FinanceQuarterImpl
       implements FinanceQuarterService {
  @Autowired
  private FinanceQuarterRepository financeQuarterRepository;

  @Override
  public List<FinanceQuarter> loadAll() {
    return financeQuarterRepository.findAll();
  }
}
