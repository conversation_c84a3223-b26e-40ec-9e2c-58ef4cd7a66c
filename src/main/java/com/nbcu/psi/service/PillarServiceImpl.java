package com.nbcu.psi.service;

import com.nbcu.psi.model.Pillar;
import com.nbcu.psi.repositories.PillarRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class PillarServiceImpl
       implements PillarService {
  @Autowired
  private PillarRepository pillarRepository;

  @Override
  public List<Pillar> loadAll() {
    return pillarRepository.findAll();
  }
}
