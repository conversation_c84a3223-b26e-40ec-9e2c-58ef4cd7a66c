package com.nbcu.psi.service;

import com.nbcu.psi.model.RatingStream;
import com.nbcu.psi.repositories.RatingStreamRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class RatingStreamServiceImpl
       implements RatingStreamService {
  @Autowired
  private RatingStreamRepository ratingStreamRepository;

  @Override
  public List<RatingStream> loadAll() {
    return ratingStreamRepository.findAll();
  }
}
