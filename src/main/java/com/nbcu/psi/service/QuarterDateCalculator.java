package com.nbcu.psi.service;
import com.nbcu.psi.service.*;
import org.springframework.stereotype.Service;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import com.nbcu.psi.*;
import java.time.*;
import java.util.*;
import static java.time.temporal.IsoFields.QUARTER_OF_YEAR;

@Service
@RequiredArgsConstructor
public class QuarterDateCalculator {
  @NonNull
  public DataService dataService;

  /**
   * Implements the dynamic quarter start/end date calculation originally mentioned in PAM-172
   * First based on year and quarterAmounts, it will find the first quarter and last quarter that has non-zero amount.
   * Then if isDigital is true, it will use gregorianStartDate and gregorianEndDate to calculate the start and end date.
   * Otherwise, it will use fetchQuarterStartDate and fetchQuarterEndDate to calculate the start and end date. 
   * @param years like '2022/2023'
   * @param quarterAmounts  like [0, 1000, 0, 2000, 0, 0]
   * @param property_id like 1032
   * @param isDigital like true
   * @return
   */
  public LocalDate[] getStartEnd(String years, int[] quarterAmounts, int property_id, boolean isDigital) {
    int[][] yqs = convertYearQuarterFromAmounts(years, quarterAmounts);
    Tool.pf("yqs:%d %d %d %d", yqs[0][0], yqs[0][1], yqs[1][0], yqs[1][1]);
    LocalDate startDate = null;
    LocalDate endDate = null;
    if (isDigital) {
      startDate = gregorianStartDate(yqs[0][0], yqs[0][1]);
      endDate = gregorianEndDate(yqs[1][0], yqs[1][1]);
    } else {
      startDate = fetchQuarterStartDate(yqs[0][0], yqs[0][1], property_id);
      endDate = fetchQuarterEndDate(yqs[1][0], yqs[1][1], property_id);
    }
    return new LocalDate[] {startDate, endDate};
  }

  /**
   * query database for the start date of the quarter
   * @param year
   * @param quarter
   * @param property_id
   * @return
   */
  public LocalDate fetchQuarterStartDate(int year, int quarter, int property_id) {
    LocalDate[] dates = dataService.fetchCombinedQuarterDates(year, quarter, property_id);
    return dates[0];
  }

  /**
   * query database for the end date of the quarter
   * @param year
   * @param quarter
   * @param property_id
   * @return
   */
  public LocalDate fetchQuarterEndDate(int year, int quarter, int property_id) {
    LocalDate[] dates = dataService.fetchCombinedQuarterDates(year, quarter, property_id);
    return dates[1];
  }

  /**
   * Calculate greogorian start date of the quarter
   * @param year
   * @param quarter
   * @return
   */
  public static LocalDate gregorianStartDate(int year, int quarter) {
    int startMonth = quarter * 3 - 2;
    LocalDate d = YearMonth.of(year, startMonth).atDay(1);
    return d;
  }

  /**
   * Calculate greogorian end date of the quarter
   * @param year
   * @param quarter
   * @return
   */
  public static LocalDate gregorianEndDate(int year, int quarter) {
      int endMonth = quarter * 3;
      LocalDate d = YearMonth.of(year, endMonth).atEndOfMonth();
      return d;
  }

  /**
   * 
   * @param years like '2022/2023'
   * @param quarterAmounts like [0, 1000, 0, 2000, 0, 0]
   * @return
   */
  public static int[][] convertYearQuarterFromAmounts(String years, int[] quarterAmounts) {
    String[] pycy = years.split("/");
    int py = Integer.parseInt(pycy[0]); // get pyear
    return convertYearQuarterFromAmounts(py, quarterAmounts);
  }

  /**
   * returns [[start_year, start_quarter], [end_year, end_quarter]]
   * ex. [[2021, 3], [2022, 4]]
   * @param py like 2022
   * @param quarterAmounts
   * @return
   */
  public static int[][] convertYearQuarterFromAmounts(int py, int[] quarterAmounts) {
    int cy = py + 1;
    int[] yearList = { py, py, cy, cy, cy, cy};
    int[] quarterList = {3, 4, 1, 2, 3, 4};
    
    int startIdx = 0;
    int endIdx = quarterAmounts.length - 1;
    for (int i = quarterAmounts.length -1; i >=0; i--) {
      if (quarterAmounts[i] > 0) {
        startIdx = i;
      }
    }
    for (int i = 0; i < quarterAmounts.length; i++) {
      if (quarterAmounts[i] > 0) {
        endIdx = i;
      }
    }
    int[][] yqs = {{yearList[startIdx], quarterList[startIdx]}, 
                   {yearList[endIdx], quarterList[endIdx]}};
    return yqs;

  }

}
