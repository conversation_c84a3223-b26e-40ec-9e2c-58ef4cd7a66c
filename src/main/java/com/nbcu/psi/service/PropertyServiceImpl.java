package com.nbcu.psi.service;

import com.nbcu.psi.model.Property;
import com.nbcu.psi.repositories.PropertyRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class PropertyServiceImpl
       implements PropertyService {
  @Autowired
  private PropertyRepository propertyRepository;

  @Override
  public List<Property> loadAll() {
    return propertyRepository.findAll();
  }
}
