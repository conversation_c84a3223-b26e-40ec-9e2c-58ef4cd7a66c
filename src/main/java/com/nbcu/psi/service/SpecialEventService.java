package com.nbcu.psi.service;

import com.nbcu.psi.model.SpecialEvent;
import com.nbcu.psi.repositories.SpecialEventRepository;
import org.springframework.stereotype.Service;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class SpecialEventService {

    @NonNull
    private SpecialEventRepository repository;

    public SpecialEvent findById(long id) {
        return repository.findById(id).orElse(null);
    }
}