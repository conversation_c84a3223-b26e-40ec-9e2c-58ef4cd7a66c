package com.nbcu.psi.service;

import com.nbcu.psi.model.Advertiser;
import com.nbcu.psi.repositories.AdvertiserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class AdvertiserServiceImpl
       implements AdvertiserService {
  @Autowired
  private AdvertiserRepository advertiserRepository;

  @Override
  public List<Advertiser> loadAll() {
    return advertiserRepository.findAll();
  }

  public List<Advertiser> findByName(String name) {
    return advertiserRepository.findByAdvertiserName(name);
  }
  
}
