package com.nbcu.psi.service;

import com.nbcu.psi.model.ProductCategory;
import com.nbcu.psi.repositories.ProductCategoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class ProductCategoryServiceImpl implements ProductCategoryService {
    
    @Autowired
    private ProductCategoryRepository productCategoryRepository;
    
    @Override
    public List<ProductCategory> loadAll() {
        return productCategoryRepository.findAll();
    }
}