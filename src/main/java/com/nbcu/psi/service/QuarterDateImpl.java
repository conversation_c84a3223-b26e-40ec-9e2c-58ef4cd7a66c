package com.nbcu.psi.service;

import com.nbcu.psi.model.QuarterDate;
import com.nbcu.psi.repositories.QuarterDateRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class QuarterDateImpl
       implements QuarterDateService {
  @Autowired
  private QuarterDateRepository quarterDateRepository;

  @Override
  public List<QuarterDate> loadAll() {
    return quarterDateRepository.findAll();
  }
}
