package com.nbcu.psi.service;

import com.nbcu.psi.model.SellingVertical;
import com.nbcu.psi.repositories.SellingVerticalRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class SellingVerticalServiceImpl implements SellingVerticalService {
  @Autowired
  private SellingVerticalRepository sellingVerticalRepository;

  @Override
  public List<SellingVertical> loadAll() {
    return sellingVerticalRepository.findAll();
  }
}
