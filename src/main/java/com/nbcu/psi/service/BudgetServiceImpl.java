package com.nbcu.psi.service;

import com.nbcu.psi.model.Budget;
import com.nbcu.psi.repositories.BudgetRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class BudgetServiceImpl
       implements BudgetService {
  @Autowired
  private BudgetRepository budgetRepository;

  @Override
  public List<Budget> loadAll() {
    return budgetRepository.findAll();
  }

  public Budget findById(long id) {
    return budgetRepository.findById(id).get();
  }

  public List<Long> findUpdatedBudgetIds(String dateStr) {
    return budgetRepository.findUpdatedBudgetIds(dateStr);
  }
}
