package com.nbcu.psi.service;

import com.nbcu.psi.model.QuarterDateOverride;
import com.nbcu.psi.repositories.QuarterDateOverrideRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;


@Service
public class QuarterDateOverrideImpl
       implements QuarterDateOverrideService {
  @Autowired
  private QuarterDateOverrideRepository quarterDateOverrideRepository;

  @Override
  public List<QuarterDateOverride> loadAll() {
    return quarterDateOverrideRepository.findAll();
  }

  @Override
  public QuarterDateOverride findByFinanceQuarterIdAndPropertyId(Integer financeQuarterId, Integer propertyId) {
    return quarterDateOverrideRepository.findByFinanceQuarterIdAndPropertyId(BigDecimal.valueOf(financeQuarterId), BigDecimal.valueOf((propertyId)));
  }
}
