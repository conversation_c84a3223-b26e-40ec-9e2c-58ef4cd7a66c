package com.nbcu.psi.service;

import com.nbcu.psi.model.DealLinkAsc;
import com.nbcu.psi.repositories.DealLinkAscRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class DealLinkAscServiceImpl
       implements DealLinkAscService {
  @Autowired
  private DealLinkAscRepository dealLinkAscRepository;

  @Override
  public List<DealLinkAsc> loadAll() {
    return dealLinkAscRepository.findAll();
  }
}
