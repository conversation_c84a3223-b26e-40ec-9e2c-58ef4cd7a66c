package com.nbcu.psi.service;

import com.nbcu.psi.model.AppRole;
import com.nbcu.psi.repositories.AppRoleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class AppRoleServiceImpl
       implements AppRoleService {
  @Autowired
  private AppRoleRepository appRoleRepository;

  @Override
  public List<AppRole> loadAll() {
    return appRoleRepository.findAll();
  }
}
