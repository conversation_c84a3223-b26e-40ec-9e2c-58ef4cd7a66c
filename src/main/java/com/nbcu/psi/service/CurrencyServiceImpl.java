package com.nbcu.psi.service;

import com.nbcu.psi.model.Currency;
import com.nbcu.psi.repositories.CurrencyRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class CurrencyServiceImpl
       implements CurrencyService {
  @Autowired
  private CurrencyRepository currencyRepository;

  @Override
  public List<Currency> loadAll() {
    return currencyRepository.findAll();
  }
}
