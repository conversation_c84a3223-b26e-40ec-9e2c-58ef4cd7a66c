package com.nbcu.psi.service;

import com.nbcu.psi.model.SystemKeyAsc;
import com.nbcu.psi.repositories.SystemKeyAscRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class SystemKeyAscServiceImpl
       implements SystemKeyAscService {
  @Autowired
  private SystemKeyAscRepository systemKeyAscRepository;

  @Override
  public List<SystemKeyAsc> loadAll() {
    return systemKeyAscRepository.findAll();
  }

  public SystemKeyAsc findByAdvertiserId(long advertiserId) {
    return systemKeyAscRepository.findByAdvertiserId(advertiserId);
  }
  public SystemKeyAsc findByAgencyId(long agencyId) {
    return systemKeyAscRepository.findByAgencyId(agencyId);
  }
  public SystemKeyAsc findByPropertyId(long propertyId) {
    return systemKeyAscRepository.findByPropertyId(propertyId);
  }
}
