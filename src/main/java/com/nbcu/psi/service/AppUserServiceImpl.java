package com.nbcu.psi.service;

import com.nbcu.psi.model.AppUser;
import com.nbcu.psi.repositories.AppUserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class AppUserServiceImpl
       implements AppUserService {
  @Autowired
  private AppUserRepository appUserRepository;

  @Override
  public List<AppUser> loadAll() {
    return appUserRepository.findAll();
  }
}
