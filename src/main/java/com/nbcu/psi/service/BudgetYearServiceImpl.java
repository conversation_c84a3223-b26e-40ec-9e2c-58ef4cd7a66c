package com.nbcu.psi.service;

import com.nbcu.psi.model.BudgetYear;
import com.nbcu.psi.model.FinanceQuarter;
import com.nbcu.psi.model.Quarter;
import com.nbcu.psi.model.QuarterDateOverride;
import com.nbcu.psi.model.QuarterMapping;
import com.nbcu.psi.model.QuarterMapping.CyPy;
import com.nbcu.psi.model.QuarterMapping.DateRange;
import com.nbcu.psi.repositories.BudgetYearRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static java.time.temporal.IsoFields.QUARTER_OF_YEAR;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;
import java.util.Optional;


@Service
public class BudgetYearServiceImpl implements BudgetYearService {
  static final Integer START_LOOKUP_QUARTER_NUMBER = 3;
  static final Integer END_LOOKUP_QUARTER_NUMBER = 4;

  @Autowired
  private BudgetYearRepository budgetYearRepository;

  @Autowired
	private QuarterService quarterService;

  @Autowired
	private QuarterDateOverrideService quarterDateOverrideService;

  @Override
  public List<BudgetYear> loadAll() {
    return budgetYearRepository.findAll();
  }

  @Override
  public Optional<BudgetYear> findById(long id) {
      return budgetYearRepository.findById(id);
  }  

  public LocalDate startDate(Integer budgetYear, Integer propertyId, Boolean isDigital, QuarterMapping mapping) {
    if (isDigital) {
      return digitalQuarterDate(budgetYear, mapping, DateRange.START_DATE);
    } else {
      return linearQuarterDate(budgetYear, propertyId, mapping, DateRange.START_DATE);
    }
  }

  public LocalDate endDate(Integer budgetYear, Integer propertyId, Boolean isDigital, QuarterMapping mapping) {
    if (isDigital) {
      return digitalQuarterDate(budgetYear, mapping, DateRange.END_DATE);
    } else {
      return linearQuarterDate(budgetYear, propertyId, mapping, DateRange.END_DATE);
    }
  }

  private LocalDate linearQuarterDate(Integer budgetYear, Integer propertyId, QuarterMapping mapping, DateRange startOrEnd) {
    Quarter quarter = quarterService.findByYearAndQuarterNumber(
                        year(budgetYear, mapping.cypy),
                        mapping.quarter
                      );
    FinanceQuarter financeQuarter = quarter.getFinanceQuarters().get(0);
    QuarterDateOverride quarterDateOverride = quarterDateOverrideService.findByFinanceQuarterIdAndPropertyId(
                                                (int)financeQuarter.getFinanceQuarterId(),
                                                propertyId
                                              );
    if (quarterDateOverride != null){
      return DateRange.START_DATE.equals(startOrEnd)? quarterDateOverride.getQuarterStartDate() : quarterDateOverride.getQuarterEndDate();
    } else {
      return DateRange.START_DATE.equals(startOrEnd)? financeQuarter.getQuarterDates().get(0).getQuarterStartDate() : financeQuarter.getQuarterDates().get(0).getQuarterEndDate();
    }
  }

  private LocalDate digitalQuarterDate(Integer budgetYear, QuarterMapping mapping, DateRange startOrEnd) {
    LocalDate digitalQuarterDate = null;

    if (DateRange.START_DATE.equals(startOrEnd)) {
      Integer startMonth = mapping.quarter * 3 - 2;
      digitalQuarterDate = YearMonth.of(
                             year(budgetYear, mapping.cypy),
                             startMonth)
                           .with(
                             QUARTER_OF_YEAR,
                             mapping.quarter)
                           .atDay(1);
    } else {
      Integer endMonth = mapping.quarter * 3;
      digitalQuarterDate = YearMonth.of(
                             year(budgetYear, mapping.cypy),
                             endMonth)
                           .with(
                             QUARTER_OF_YEAR,
                             mapping.quarter)
                           .atEndOfMonth();
    }

    return digitalQuarterDate;
  }

  private Integer year(Integer budgetYear, CyPy cypy) {
    return CyPy.CY.equals(cypy)? budgetYear : budgetYear - 1;
  }

  public BudgetYear fetchCurrentBudgetYear() {
    return budgetYearRepository.fetchCurrentBudgetYear();
  }

}
