package com.nbcu.psi.service;

import com.nbcu.psi.model.RoleEntitlement;
import com.nbcu.psi.repositories.RoleEntitlementRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class RoleEntitlementImpl
       implements RoleEntitlementService {
  @Autowired
  private RoleEntitlementRepository roleEntitlementRepository;

  @Override
  public List<RoleEntitlement> loadAll() {
    return roleEntitlementRepository.findAll();
  }
}
