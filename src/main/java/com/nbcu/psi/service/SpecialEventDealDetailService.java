package com.nbcu.psi.service;

import com.nbcu.psi.model.SpecialEventDealDetail;
import com.nbcu.psi.repositories.SpecialEventDealDetailRepository;
import org.springframework.stereotype.Service;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import java.util.List;

@Service
@RequiredArgsConstructor
public class SpecialEventDealDetailService {

    @NonNull
    private SpecialEventDealDetailRepository repository;

    public List<SpecialEventDealDetail> findBySpecialEventDealId(long dealId) {
        return repository.findBySpecialEventDealId(dealId);
    }
    
    public List<SpecialEventDealDetail> findByStatusAndLastSyncDate(String status, String lastSyncDate) {
        return repository.findByStatusAndLastSyncDate(status, lastSyncDate);
    }
}