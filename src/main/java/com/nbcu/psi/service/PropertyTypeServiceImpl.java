package com.nbcu.psi.service;

import com.nbcu.psi.model.PropertyType;
import com.nbcu.psi.repositories.PropertyTypeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class PropertyTypeServiceImpl
       implements PropertyTypeService {
  @Autowired
  private PropertyTypeRepository propertyTypeRepository;

  @Override
  public List<PropertyType> loadAll() {
    return propertyTypeRepository.findAll();
  }
}
