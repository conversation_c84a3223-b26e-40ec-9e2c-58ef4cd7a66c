package com.nbcu.psi.service;

import com.nbcu.psi.model.Quarter;
import com.nbcu.psi.repositories.QuarterRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;


@Service
public class QuarterServiceImpl implements QuarterService {
  @Autowired
  private QuarterRepository quarterRepository;

  @Override
  public List<Quarter> loadAll() {
    return quarterRepository.findAll();
  }

  @Override
  public Quarter findByYearAndQuarterNumber(Integer year, Integer quarterNumber) {
    return quarterRepository.findByYearAndQuarter(BigDecimal.valueOf(year), BigDecimal.valueOf((quarterNumber))).get(0);
  }

  @Override
  public Optional<Quarter> findById(Long id) {
    return quarterRepository.findById(id);
  }
}
