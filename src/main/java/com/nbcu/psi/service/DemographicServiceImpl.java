package com.nbcu.psi.service;

import com.nbcu.psi.model.Demographic;
import com.nbcu.psi.repositories.DemographicRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class DemographicServiceImpl
       implements DemographicService {
  @Autowired
  private DemographicRepository demographicRepository;

  @Override
  public List<Demographic> loadAll() {
    return demographicRepository.findAll();
  }
}
