package com.nbcu.psi.service;
import com.nbcu.psi.Tool;
import com.nbcu.psi.model.Deal;
import com.nbcu.psi.model.DealLink;
import com.nbcu.psi.model.DealLinkDaypartRecord;
import com.nbcu.psi.model.DealLinkRecord;
import com.nbcu.psi.model.DealRecord;
import com.nbcu.psi.model.DealThroughParentDao;
import com.nbcu.psi.model.ParentDealRecord;
import com.nbcu.psi.repositories.DealRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Service
public class DealServiceImpl
       implements DealService {
  @Autowired
  private DealRepository dealRepository;

  @Override
  public List<Deal> loadAll() {
    return dealRepository.findAll();
  }
}
