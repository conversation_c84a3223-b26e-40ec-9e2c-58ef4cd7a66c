package com.nbcu.psi.service;

import com.nbcu.psi.model.Entitlement;
import com.nbcu.psi.repositories.EntitlementRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class EntitlementServiceImpl
  implements EntitlementService {
  @Autowired
  private EntitlementRepository entitlementRepository;

  @Override
  public List<Entitlement> loadAll() {
    return entitlementRepository.findAll();
  }

  public List<Entitlement> findByName(String name) {
    return entitlementRepository.findByEntitlementName(name);
  }
  
}
