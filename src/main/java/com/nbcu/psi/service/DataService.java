package com.nbcu.psi.service;
import com.nbcu.psi.Tool;
import com.nbcu.psi.model.*;
import com.nbcu.psi.repositories.*;
import com.nbcu.psi.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.core.env.Environment;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import lombok.NonNull;
import java.time.LocalDate;
import lombok.RequiredArgsConstructor;



@Service
@RequiredArgsConstructor
public class DataService {
  @NonNull
  private DataRepository dataRepository;

  @NonNull
  private BudgetYearService budgetYearService;

  @NonNull
  private Environment environment;

  public List<ParentDealRecord> fetchParentDealRecords(int budgetYearId, String lastUpdatedDate) {
    String stageName = environment.getProperty("app.stagename");
    List<Object[]> dls = dataRepository.fetchDealsThroughParent(budgetYearId, lastUpdatedDate, stageName);
    return organizeRecords(dls);
  }

  public List<ParentDealRecord> fetchSyncParentDealRecords(int budgetYearId, String lastUpdatedDate) {
    String stageName = environment.getProperty("app.stagename");
    List<Object[]> dls = dataRepository.fetchSyncDealsThroughParent(budgetYearId, lastUpdatedDate, stageName);
    return organizeRecords(dls);
  }  

  public List<ParentDealRecord> organizeRecords(List<Object[]> dls) {
    List<ParentDealRecord> parents = new ArrayList<ParentDealRecord>();
    for (Object[] dl: dls) {
      DealThroughParentDao dao = new DealThroughParentDao(dl);
      long ParentDealId = dao.getParentDealId();
      ParentDealRecord parent = findOrCreateParentDealRecord(parents, dao);
      if (dao.getDealLinkId() > 0) { // deal link
        DealLinkRecord dealLink = parent.findOrCreateDealLinkRecord(dao);
        dealLink.setDealLinkName(dao.getDealLinkName());
        DealRecord deal = new DealRecord(dao);
        dealLink.addDeal(deal);
      } else { // single deal
        DealRecord deal = new DealRecord(dao);
        if (deal.shouldSend()) {
          parent.addDeal(deal);
        }
      }
    }
    for (ParentDealRecord parent: parents) {
      parent.aggregateDeals();
    }

    // filter out those parent deals that isEmpty
    parents = parents.stream().filter(p -> !p.isEmpty()).collect(Collectors.toList());
    
    return parents;

  }

  public ParentDealRecord findOrCreateParentDealRecord(List<ParentDealRecord> records, DealThroughParentDao dao) {
    long parentDealId = dao.getParentDealId();
    for (ParentDealRecord record: records) {
      if (record.getParentDealId() == parentDealId) {
        return record;
      }
    }
    ParentDealRecord record = new ParentDealRecord(dao);
    records.add(record);
    return record;
  }

  public LocalDate[] fetchCombinedQuarterDates(int year, int quarter, int property_id) {
    List<Object[]> objs = dataRepository.fetchCombinedQuarterDates(year, quarter, property_id);
    Object[] o = objs.get(0);
    int idx = 0; 
    LocalDate startDate = Tool.timestampToLocalDate(o[idx++]);
    LocalDate endDate = Tool.timestampToLocalDate(o[idx++]);
    return new LocalDate[] {startDate, endDate};

  }

  /**
   * Fetch the budget year name for the default budget year such as 2023/2024
   * @return
   */
  public BudgetYear fetchCurrentBudgetYear() {
    return budgetYearService.fetchCurrentBudgetYear();
  }

  public String resultToString(List<Object[]> objs) {
    Object[] o = objs.get(0);
    return (String) o[0];
  }
}
