package com.nbcu.psi.service;
import com.nbcu.psi.model.BudgetYear;
import com.nbcu.psi.model.QuarterMapping;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;


public interface BudgetYearService {
  List<BudgetYear> loadAll();
  Optional<BudgetYear> findById(long id);
  LocalDate startDate(Integer year, Integer propertyId, Boolean isDigital, QuarterMapping mapping);
  LocalDate endDate(Integer year, Integer propertyId, Boolean isDigital, QuarterMapping mapping);
  public BudgetYear fetchCurrentBudgetYear();
}
