package com.nbcu.psi.service;

import com.nbcu.psi.model.Agency;
import com.nbcu.psi.repositories.AgencyRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class AgencyServiceImpl
       implements AgencyService {
  @Autowired
  private AgencyRepository agencyRepository;

  @Override
  public List<Agency> loadAll() {
    return agencyRepository.findAll();
  }
}
