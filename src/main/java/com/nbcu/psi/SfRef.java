package com.nbcu.psi;

import java.util.ArrayList;
import java.util.List;

/**
 * SfRef is a reference database to a Salesforce object in payload creation.
 * RefType could be: 
 * sf_deal, 
 * sf_opportunity_deal, 
 * sf_opportunity_lineitem_deal, 
 * sf_opportunity_deal_link, 
 * sf_opportunity_lineitem_deal_link
 * sf_product2
 * sf_product2_lookup
 * sf_pricebookentry_lookup
 * sf_daypart
 * sf_update_opportunity_deal
 * sf_update_opportunity_deal_link
 */
public class SfRef {
  // public static int refCount = 0;
  public static List<SfRef> records = new ArrayList<SfRef>();
  public String refType = null;
  public String referenceId = null;
  public String lookupId = null;
  public SfRef(String refType, String lookupId) {
    this.refType = refType;
    this.lookupId = lookupId;
    this.referenceId = Tool.f("%03d_%s",  records.size(), refType);
    records.add(this);
  }

  public static void reset() {
    records = new ArrayList<SfRef>();
  }

  public String toString() {
    return Tool.f("SfRef(%s %s %s)", refType, lookupId, referenceId);
  }

  public static SfRef getRef(String refType, String lookupId) throws Exception {
    for (SfRef record: records) {
      if (refType.equals(record.refType) && lookupId.equals(record.lookupId)){
        return record;
      }
    }
    throw new Exception(Tool.f("getRef('%s', '%s') not found: ", refType, lookupId));
  }

  public static SfRef getOrCreateRef(String refType, String lookupId) {
    SfRef record = null;
    try {
      record = getRef(refType, lookupId);
    } catch (Exception e) {
      record = new SfRef(refType, lookupId);
    }
    return record;
  }

  public static SfRef getRefById(String referenceId) throws Exception {
    for (SfRef record: records) {
      if (referenceId.equals(record.referenceId)){
        return record;
      }
    }
    throw new Exception(Tool.f("getRefById('%s') not found: ", referenceId));
  }

  /**
   * List all SfRef records for debugging
   * @return
   */
  public static String listAll() {
    StringBuffer sb = new StringBuffer();
    for (SfRef record: records) {
      sb.append(record.toString() + "\n");
    }
    return sb.toString();
  }
}
