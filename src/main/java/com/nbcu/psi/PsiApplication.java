package com.nbcu.psi;

import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.stream.Collectors;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;

import com.nbcu.psi.service.*;
import com.nbcu.psi.model.*;
import com.nbcu.psi.mail.notification.*;
import com.nbcu.psi.pam.PamApiClient;
import com.nbcu.psi.s3.S3Util;
import com.nbcu.psi.sf.SfApiException;
import com.nbcu.psi.sf.SfOtherException;
import com.nbcu.psi.sf.SfClient;
import com.nbcu.psi.sf.SfLogic;

import org.springframework.core.env.Environment;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
@SpringBootApplication
@RequiredArgsConstructor
public class PsiApplication implements CommandLineRunner{
  public static String PARENT_DEAL_LAST_TIME = "psi_parent_deal_link_last_processed.txt";
  public static String SYNC_PARENT_DEAL_LAST_TIME = "psi_sync_parent_deal_link_last_processed.txt";
  public static String SYNC_OPPORTUNITY_LAST_TIME = "psi_sync_opportunity_last_processed.txt";
  public static String SYNC_SPECIAL_EVENT_LAST_TIME = "psi_sync_special_event_last_processed.txt";

  @NonNull
  private Global g;

  @NonNull
  public Logger logger;

  @NonNull
  private S3Util s3Util;

  @NonNull
  private PamApiClient pamApiClient;

  @NonNull
  private BudgetService budgetService;

  @NonNull
  private QuarterDateCalculator quarterDateCalculator;

  @NonNull
  private DataService dataService;

  @NonNull
  private DealLinkService dealLinkService;

  @NonNull
  private EngineeringErrorNotification engineeringErrorNotification;

  @NonNull
  private SfClient sfClient;

  @NonNull
  private SfLogic sfLogic;

  @NonNull
  private Environment env;

  @NonNull
  Environment environment;

  @NonNull
  private SpecialEventSyncLogic specialEventSyncLogic;

  public String budgetYearName = null;
  public String dealYear = null;

  public static void main(String[] args) {
    SpringApplication.run(PsiApplication.class, args);
  }

  public void run(String... args){
    Tool.pr("Version: sync opportunity stage");
    mainProgram();
  }

  // for storing each of the failed parent deal processing for email notification
  ArrayList<ResultRecord> failures = new ArrayList<ResultRecord>();

  /**
   * This is the main loop of the program.
   * It reads all changed parent deal since last run, then send insert to salesforce composite api.
   * It will collect exceptions of each composite api call, which will be handled differently from other exceptions.
   */
  public void mainProgram() {
    g.setLocal("true" == this.environment.getProperty("islocal"));

    long startTime = new Date().getTime();
    Tool.pr("--Start Program--");
    Tool.pr("Timezone:" + g.getTz());

    try {
      syncOpportunityStage();
      processParentDeals();
      processSyncParentDeals();
      syncSpecialEvents();
    } catch (Exception e) {
      Tool.pr(Tool.getExceptionStr(e));
    }
    finally {
      String timeStr = Tool.timeDiffStr(startTime, new Date().getTime());
      Tool.pr("--Exit Program-- finished in:" + timeStr);
      System.exit(0);
    }
  }

  public void syncSpecialEvents() {
    try {
        String lastProcessDateStr = readLastProcessedDateStr(SYNC_SPECIAL_EVENT_LAST_TIME);
        Tool.pr("last time sync Special Event DateStr:" + lastProcessDateStr);
        
        prepare();
        
        specialEventSyncLogic.processPendingDetails(lastProcessDateStr);
        
        String currentDateStr = Tool.getTimeStr(g.getTz());
        writeLastProcessedDateStr(SYNC_SPECIAL_EVENT_LAST_TIME, currentDateStr);
        
    } catch (Exception e) {
        processException(e);
    }
  }  

  /**
   * Test to see if pam-pai and salesforce are working.
   * @throws Exception
   */
  public void prepare() throws Exception {
    logger.createDumpFolder();

    Tool.pf("budget_year: %s(%s), dealYear: %s", g.budgetYearName, g.budgetYearId, g.dealYear);

    sfClient.getAccessToken();
    Tool.pr("connected to salesforce");
    // sfClient.printConfig();

    pamApiClient.healthCheck();
    Tool.pr("connected to pam-pai");
  }

  /**
   * Use the last process time str to query salesforce for opportunityhistory records.
   * For those opportunities, check if stagename matches the listed values. 
   * If so, collect the pam deal_ids and call pam_api to update sf_sync_status to 'wml'.
   * @throws Exception
   */
  public void syncOpportunityStage() {
    try {
      g.setCurrentBudgetYear(dataService.fetchCurrentBudgetYear());

      String lastProcessDateStr = readLastProcessedDateStr(SYNC_OPPORTUNITY_LAST_TIME);
      Tool.pr("last time sync Opportunity DateStr:" + lastProcessDateStr);
      prepare();
      String stageNamesConfig = environment.getProperty("app.sync-stage-names");
      String[] stageNames = stageNamesConfig.split(",");
      String stageNamesQueryPart = Arrays.stream(stageNames)
                                          .map(name -> "'" + name.trim() + "'")
                                          .collect(Collectors.joining(", "));
      String soql = "SELECT Opportunity.StageName, Opportunity.PAM_Deal_Link__c, Opportunity.PAM_Deal_ID__c " + 
        "FROM OpportunityHistory " + 
        "WHERE Opportunity.PAM_Parent_Deal_ID__c LIKE '%-" +  g.dealYear + "' " +
        "AND Opportunity.StageName IN (" + stageNamesQueryPart + ") " + 
        "AND CreatedDate > " +  Tool.convertToIso8601(lastProcessDateStr) + " " + 
        "ORDER BY CreatedDate DESC ";
      Tool.pr(soql);

      Set<String> dealIds = new HashSet<>(); 
      List<Map<String, Object>> records = sfClient.fetchBySOQL(soql);

      Tool.pr("Total Records: " + records.size());
      for (int i = 0; i < records.size(); i++) {
        Map<String, Object> record = records.get(i);
        record.forEach((key, value) -> System.out.println(key + ": " + value));        
        Map<String, Object> opportunity = (Map<String, Object>)record.get("Opportunity");
        String dealIdC = (String)opportunity.get("PAM_Deal_Id__c");
        if (dealIdC != null) {
          String dealId = dealIdC.substring(0, dealIdC.indexOf("-"));
          if (dealId.matches("\\d{4,}")) {
            dealIds.add(dealId); 
          }
        }
      }      
      // Concatenate all collected unique deal IDs into a comma-separated string
      if (!dealIds.isEmpty()) {
        String dealIdsStr = String.join(",", dealIds);
        try {
          pamApiClient.updateBudgetsSyncStatusByDealId(dealIdsStr, "wml");
        } catch(Exception e) {
          Tool.pr("Failed to update budget sync status for deal IDs using pamAPI: " + dealIdsStr);
        }
        
        Tool.pr("Concatenated Unique Deal IDs: " + dealIdsStr);      
      } else {
        Tool.pr("No Deal IDs found");
      }
      String currentDateStr = Tool.getTimeStr(g.getTz()); 
      writeLastProcessedDateStr(SYNC_OPPORTUNITY_LAST_TIME, currentDateStr);
    }
    catch (Exception e) { 
      // if we get here, there must be a systematic error.
      processException(e);
    }
    finally {
      // not matter if there is systematic error, accumulated sf exceptions will be sent out as single email.
      if (failures.size() > 0) {
        processSfException(failures);
      }
    }
  }
      
  /**
   * Use the last process time str to do the query. If there are data to process,
   * then use prepare function to test if pam-pai and salesforce are all working.
   * If all goes well, then write the last process time str back.
   * @throws Exception
   */
  public void processParentDeals() {
    try {
      g.setCurrentBudgetYear(dataService.fetchCurrentBudgetYear());

      String lastProcessDateStr = readLastProcessedDateStr(PARENT_DEAL_LAST_TIME);
      Tool.pr("lastProcessed Parent Deal DateStr:" + lastProcessDateStr);
      // lastProcessDateStr = "2021-01-01 00:00:00"; // override for testing
      List<ParentDealRecord> parents = dataService.fetchParentDealRecords(g.getBudgetYearId(), lastProcessDateStr);
      Tool.pf("parents: %s", parents.size());
      if (parents.size() == 0) {
        Tool.pr("No parent deal to process");
        return;
      } else {
        String parentIds = parents.stream()
          .map(ParentDealRecord::getParentDealId).map(String::valueOf)
          .collect(Collectors.joining(", "));
        Tool.pf("parent ids: (%s)", parentIds);
      }
      // System.exit(0);
      String currentDateStr = Tool.getTimeStr(g.getTz()); //prepare takes some seconds. We don't want to leave gap.
      prepare();
      writeLastProcessedDateStr(PARENT_DEAL_LAST_TIME, currentDateStr);
      for (ParentDealRecord parent: parents) {
        processParentDeal(parent);
      }
    }
    catch (Exception e) { 
      // if we get here, there must be a systematic error.
      processException(e);
    }
    finally {
      // not matter if there is systematic error, accumulated sf exceptions will be sent out as single email.
      if (failures.size() > 0) {
        processSfException(failures);
      }
    }

  }

  /**
   * Use the last process time str to do the query. If there are data to process,
   * then use prepare function to test if pam-pai and salesforce are all working.
   * If all goes well, then write the last process time str back.
   * @throws Exception
   */
  public void processSyncParentDeals() {
    try {
      g.setCurrentBudgetYear(dataService.fetchCurrentBudgetYear());

      String lastProcessDateStr = readLastProcessedDateStr(SYNC_PARENT_DEAL_LAST_TIME);
      Tool.pr("lastProcessed Parent Deal DateStr:" + lastProcessDateStr);
      // lastProcessDateStr = "2021-01-01 00:00:00"; // override for testing
      List<ParentDealRecord> parents = dataService.fetchSyncParentDealRecords(g.getBudgetYearId(), lastProcessDateStr);
      Tool.pf("parents: %s", parents.size());
      if (parents.size() == 0) {
        Tool.pr("No parent deal to process");
        return;
      } else {
        String parentIds = parents.stream()
          .map(ParentDealRecord::getParentDealId).map(String::valueOf)
          .collect(Collectors.joining(", "));
        Tool.pf("parent ids: (%s)", parentIds);
      }
      // System.exit(0);
      String currentDateStr = Tool.getTimeStr(g.getTz()); //prepare takes some seconds. We don't want to leave gap.
      prepare();
      writeLastProcessedDateStr(SYNC_PARENT_DEAL_LAST_TIME, currentDateStr);
      for (ParentDealRecord parent: parents) {
        processSyncParentDeal(parent);
      }
    }
    catch (Exception e) { 
      // if we get here, there must be a systematic error.
      processException(e);
    }
    finally {
      // not matter if there is systematic error, accumulated sf exceptions will be sent out as single email.
      if (failures.size() > 0) {
        processSfException(failures);
      }
    }

  }

  public void processSyncParentDeal(ParentDealRecord parent) throws Exception {
    long parentDealId = -1;
    try {
      parentDealId = parent.getParentDealId();
      g.setParentDealId((int)parentDealId);
      g.setParentDealName(parent.getParentDealName());
      String logFolder = Tool.getTimestamp() + "_parent_deal_" + parentDealId;
      logger.info(Tool.f("---Syncing Parent Deal--- \n%s", parent));

      // each parent deal will be logged into a different folder, such as 20230313222836_parent_deal_9340
      logger.setLogFolder(logFolder); 

      // assuming parent deal already exist.
      // but it doesn't hurt to check if it is already in salesforce.
      // the create logic will never be executed.
      String sfDealId = sendParentDeal(parent);
      g.setSfDealId(sfDealId);

      // only need to handle with deals, not deal_links
      List<DealRecord> deals = parent.getDeals();
      for (DealRecord deal: deals) {
        // if (deal.isSfSendQuarterly() && !deal.isSfSendZeroDollars() && !deal.isPlaceholder()) {
        if (deal.isSfSendQuarterly()) {
            syncDealQuarterly(parent, deal, sfDealId);
        } else {
          syncDeal(parent, deal, sfDealId);
        }
      }
    } catch (Exception e) {
      // add to error email
      String headerStr = Tool.f("Failed to process Parent Deal(%s): %s\n", 
        parentDealId, parent.getParentDealName());

      failures.add(new ResultRecord(headerStr, logger.getErrorLog()));

      if (e instanceof SfApiException || e instanceof SfOtherException) {
        // if it is sf exception, continue with next parent deal
        // if it is other exception, propagate to upper level so that program is terminated.
      } else {
        throw e;
      }
    }
    finally {
      logger.writeSummary(); //Each parent deal logging folder has a summary log file.
      logger.reset(); //logger is per parent deal
    }
  }



  public void processParentDeal(ParentDealRecord parent) throws Exception {
    long parentDealId = -1;
    try {
      parentDealId = parent.getParentDealId();
      g.setParentDealId((int)parentDealId);
      g.setParentDealName(parent.getParentDealName());
      String logFolder = Tool.getTimestamp() + "_parent_deal_" + parentDealId;
      logger.info(Tool.f("---Sending Parent Deal--- \n%s", parent));

      // each parent deal will be logged into a different folder, such as 20230313222836_parent_deal_9340
      logger.setLogFolder(logFolder); 

      String sfDealId = sendParentDeal(parent);
      g.setSfDealId(sfDealId);

      List<DealRecord> deals = parent.getDeals();
      List<DealLinkRecord> dealLinks = parent.getDealLinks();
      for (DealRecord deal: deals) {
        if (deal.isSfSendQuarterly() && !deal.isSfSendZeroDollars() && !deal.isPlaceholder()) {
          sendDealQuarterly(parent, deal, sfDealId);
        } else {
          sendDeal(parent, deal, sfDealId);
        }
      }
      for (DealLinkRecord dealLink: dealLinks) {
        sendDealLink(parent, dealLink, sfDealId);
      }
    } catch (Exception e) {
      // add to error email
      String headerStr = Tool.f("Failed to process Parent Deal(%s): %s\n", 
        parentDealId, parent.getParentDealName());

      failures.add(new ResultRecord(headerStr, logger.getErrorLog()));

      if (e instanceof SfApiException || e instanceof SfOtherException) {
        // if it is sf exception, continue with next parent deal
        // if it is other exception, propagate to upper level so that program is terminated.
      } else {
        throw e;
      }
    }
    finally {
      logger.writeSummary(); //Each parent deal logging folder has a summary log file.
      logger.reset(); //logger is per parent deal
    }
  }

  public String sendParentDeal(ParentDealRecord parent) throws Exception {
    String fname = "parent_deal_" + parent.getParentDealId();
    try {
      String sfDealId = null;
      int parentDealId = (int)parent.getParentDealId();
      String parentDealIdWithYear = g.withYear(parentDealId);
      String soql = Tool.f("SELECT Id FROM Deal__c WHERE PAM_Parent_Deal_Id__c = '%s'", parentDealIdWithYear);
      sfDealId = sfClient.fetchSFIdBySOQL(soql);
      if (sfDealId != null) {
        logger.info(Tool.f("Parent_deal(%s) already exists in SF ad deal(%s). Skip insert.", parentDealIdWithYear, sfDealId));
      } else {
        sfDealId = sfLogic.sendParentDeal(parent);
        logger.successPayload(fname);

        logger.debug(Tool.f("Successfully inserted PAM parent deal(%s) as SF deal (%s)", parentDealId, sfDealId));

        pamApiClient.updateParentDeal((int)parentDealId, sfDealId); 
        logger.debug(Tool.f("Successfully updated PAM parent_deal(%s) with SF deal id(%s)", parentDealId, sfDealId));        
      }
      return sfDealId;
    } catch (SfApiException sfe) {
      logger.failurePayload(fname, sfe);
      throw sfe;
    } catch (Exception ex) {
      logger.exceptionPayload(fname, ex);
      throw ex;
    }
  }

  /**
   * TODO: sf_send_quarterly = true, but if all quarter 0, or all previous quarter 0, or send_zero = true. 
   * Then shouldn't be here. Should be sendDeal
   * @param parent
   * @param deal
   * @param sfDealId
   * @return
   * @throws Exception
   */
  public String sendDealQuarterly(ParentDealRecord parent, DealRecord deal, String sfDealId) throws Exception {
    String fname = "deal_quarterly_" + deal.getDealId();
    try {
      String sfOppId = null;
      int parentDealId = (int)parent.getParentDealId();
      String parentDealIdWithYear = g.withYear(parentDealId);
      String dealIdWithYear = g.withYear(deal.getDealId());
      // search for one of PAM_Deal_Id__c like '2021-1234-pre' or '2021-1234-post' or '2021-1234-q1'
      // as long as one opportunity has this pam_deal_id, will assume the deal is already sent to salesforce
      String soql = Tool.f("SELECT Id FROM Opportunity WHERE PAM_Parent_Deal_Id__c = '%s' and PAM_Deal_Id__c like '%s-%%'", parentDealIdWithYear, dealIdWithYear);
      sfOppId = sfClient.fetchSFIdBySOQL(soql);
      if (sfOppId != null) {
        logger.info(Tool.f("Deal(%s) already exists in SF as multiple opportunities, one of them is (%s). Skip insert.", dealIdWithYear, sfOppId));
      } else {
        sfOppId = sfLogic.sendDealQuarterly(deal, sfDealId);
        logger.successPayload(fname);
        logger.debug(Tool.f("Successfully inserted PAM deal(%s) as SF opportunity (%s)", deal.getDealId(), sfOppId));

        long budgetId = deal.getBudgetId();
        List<Integer> updatedBudgetIds = Arrays.asList((int)budgetId);
        pamApiClient.updateBudgets(updatedBudgetIds, sfDealId, sfOppId);
        logger.debug(Tool.f("Successfully updated PAM budget(%s) of deal(%s) with SF deal id(%s) and SF opportunity id(%s)", budgetId, deal.getDealId(), sfDealId, sfOppId));
      }
      return sfOppId;
    } catch (SfApiException sfe) {
      logger.failurePayload(fname, sfe);
      throw sfe;
    } catch (Exception ex) {
      logger.exceptionPayload(fname, ex);
      throw ex;
    }
  }

  public String sendDeal(ParentDealRecord parent, DealRecord deal, String sfDealId) throws Exception {
    String fname = "deal_" + deal.getDealId();
    try {
      String sfOppId = null;
      int parentDealId = (int)parent.getParentDealId();
      String parentDealIdWithYear = g.withYear(parentDealId);
      String dealIdWithYear = g.withYear(deal.getDealId());
      String soql = Tool.f("SELECT Id FROM Opportunity WHERE PAM_Parent_Deal_Id__c = '%s' and PAM_Deal_Id__c = '%s'", parentDealIdWithYear, dealIdWithYear);
      sfOppId = sfClient.fetchSFIdBySOQL(soql);
      if (sfOppId != null) {
        logger.info(Tool.f("Deal(%s) already exists in SF as opportunity(%s). Skip insert.", dealIdWithYear, sfOppId));
      } else {
        sfOppId = sfLogic.sendDeal(deal, sfDealId);
        logger.successPayload(fname);
        logger.debug(Tool.f("Successfully inserted PAM deal(%s) as SF opportunity (%s)", deal.getDealId(), sfOppId));

        long budgetId = deal.getBudgetId();
        List<Integer> updatedBudgetIds = Arrays.asList((int)budgetId);
        pamApiClient.updateBudgets(updatedBudgetIds, sfDealId, sfOppId);
        logger.debug(Tool.f("Successfully updated PAM budget(%s) of deal(%s) with SF deal id(%s) and SF opportunity id(%s)", budgetId, deal.getDealId(), sfDealId, sfOppId));
      }
      return sfOppId;
    } catch (SfApiException sfe) {
      logger.failurePayload(fname, sfe);
      throw sfe;
    } catch (Exception ex) {
      logger.exceptionPayload(fname, ex);
      throw ex;
    }
  }  

  public String sendDealLink(ParentDealRecord parent, DealLinkRecord dealLink, String sfDealId) throws Exception {
    String dealIdsStr = dealLink.getDealIds().stream().map(String::valueOf).collect(Collectors.joining("_"));
    String fname = "deal_link_" + dealLink.getDealLinkId() + "deals_" + dealIdsStr;
    try {
      String sfOppId = null;
      String parentDealIdWithYear = g.withYear(parent.getParentDealId());
      String dealLinkIdWithYear = g.withYear(dealLink.getDealLinkId());
      String soql = Tool.f("SELECT Id FROM Opportunity WHERE PAM_Parent_Deal_Id__c = '%s' and PAM_Deal_Link__c = '%s'", parentDealIdWithYear, dealLinkIdWithYear);
      sfOppId = sfClient.fetchSFIdBySOQL(soql);
      if (sfOppId != null) {
        logger.info(Tool.f("Deal link(%s) already exists in SF as opportunity(%s). Skip insert.", dealLinkIdWithYear, sfOppId));
      } else {
        sfOppId = sfLogic.sendDealLink(dealLink, sfDealId);
        logger.successPayload(fname);
        logger.debug(Tool.f("Successfully inserted PAM deal link(%s) as SF opportunity (%s)", dealLink.getDealLinkId(), sfOppId));

        List<Integer> updatedBudgetIds = dealLink.getBudgetIds();
        pamApiClient.updateBudgets(updatedBudgetIds, sfDealId, sfOppId);
        logger.debug(Tool.f("Successfully updated budgets(%s) of PAM deal link(%s) with SF deal id(%s) and SF opportunity id(%s)", updatedBudgetIds.toString(), dealLink.getDealLinkId(), sfDealId, sfOppId));
      }
      return sfOppId;
    } catch (SfApiException sfe) {
      logger.failurePayload(fname, sfe);
      throw sfe;
    } catch (Exception ex) {
      logger.exceptionPayload(fname, ex);
      throw ex;
    }
  }

  // if not split quarter, then simply update the opportunity.
  // need to recalculate start end date.
  public String syncDeal(ParentDealRecord parent, DealRecord deal, String sfDealId) throws Exception {
    String fname = "sync_deal_" + deal.getDealId();
    long budgetId = deal.getBudgetId();
    try {
      int parentDealId = (int)parent.getParentDealId();
      String parentDealIdWithYear = g.withYear(parentDealId);
      String dealIdWithYear = g.withYear(deal.getDealId());

      String sfOppId = deal.getSfOpportunityId();

      // probably don't need to verify. If doesn't exist, will let composite api to fail.
      // String soql = Tool.f("SELECT Id FROM Opportunity WHERE PAM_Parent_Deal_Id__c = '%s' and id = '%s'", parentDealIdWithYear, sfOppId);
      // Tool.pr("soql: " + soql);
      // sfOppId = sfClient.fetchSFIdBySOQL(soql);
      if (sfOppId == null) {
        throw new Exception(Tool.f("Deal(%s) doesn't have SF opportunity(%s). Skip updating.", dealIdWithYear, sfOppId));
      } else if (sfOppId.length() != 18 ) {
        throw new Exception(Tool.f("Deal(%s) May have quarterly opportunity(%s). Can't update as single opportunity. Skip updating.", dealIdWithYear, sfOppId));
      } else {
        logger.info(Tool.f("Deal(%s) exists in SF as opportunity(%s). Updating...", dealIdWithYear, sfOppId));
        sfLogic.syncDeal(deal, sfOppId);
        logger.successPayload(fname);

        // List<Integer> updatedBudgetIds = Arrays.asList((int)budgetId);

        // this is a new api, which not only updates sfOppId, but also clears sf_sync flag and set sf_synced_at.
        // pamApiClient.updateSyncBudgets(updatedBudgetIds, sfDealId, sfOppId);
        logger.debug(Tool.f("Successfully updated PAM budget(%s) of deal(%s) with SF deal id(%s) and SF opportunity id(%s)", budgetId, deal.getDealId(), sfDealId, sfOppId));
        pamApiClient.updateBudgetsSyncStatus((int)budgetId, "success");
      } 
      return sfOppId;
    } catch (SfApiException sfe) {
      pamApiClient.updateBudgetsSyncStatus((int)budgetId, "failure");
      logger.failurePayload(fname, sfe);
      throw sfe;
    } catch (SfOtherException sfe) {
      logger.exceptionPayload(fname, sfe);
      throw sfe;
    } catch (Exception ex) {
      logger.exceptionPayload(fname, ex);
      throw ex;
    }
  }  

  // if not split quarter, then simply update the opportunity.
  // need to recalculate start end date.
  public String syncDealQuarterly(ParentDealRecord parent, DealRecord deal, String sfDealId) throws Exception {
    int parentDealId = (int)parent.getParentDealId();
    String parentDealIdWithYear = g.withYear(parentDealId); // do I need these?
    String dealIdWithYear = g.withYear(deal.getDealId()); // do I need these?
    String fname = "sync_deal_quarterly_" + deal.getDealId();
    long budgetId = deal.getBudgetId();

    try {
      String sfOppId = sfLogic.syncDealQuarterly(deal);
      logger.successPayload(fname);

      List<Integer> updatedBudgetIds = Arrays.asList((int)budgetId);

      // this is a new api, which not only updates sfOppId, but also clears sf_sync flag and set sf_synced_at.
      pamApiClient.updateBudgets(updatedBudgetIds, sfDealId, sfOppId);
      logger.debug(Tool.f("Successfully updated PAM budget(%s) of deal(%s) with SF deal id(%s) and SF opportunity id(%s)", budgetId, deal.getDealId(), sfDealId, sfOppId));
      pamApiClient.updateBudgetsSyncStatus((int)budgetId, "success");
      return sfOppId;
    } catch (SfApiException sfe) {
      pamApiClient.updateBudgetsSyncStatus((int)budgetId, "failure");
      logger.failurePayload(fname, sfe);
      throw sfe;
    } catch (Exception ex) {
      logger.exceptionPayload(fname, ex);
      throw ex;
    }
  }  

  /**
   * Log and email each individual exception for each budget_id
   * @param failures record containing budget_id, operation
   */
  public void processSfException(ArrayList<ResultRecord> failures) {
    String engineerStr = "";

    for (ResultRecord r: failures) {
      engineerStr += r.headerStr;
      engineerStr += String.format("\n%s", r.errorLog);
    }

    engineerStr = String.format("<h2>PSI: Salesforce Processing Error</h2><pre style='font-family: monospace;'>%s</pre>", engineerStr);

    Tool.pr("sending engineering SalesForce exceptions:\n" + engineerStr);
    engineeringErrorNotification.send(engineerStr);
  }

  /**
   * Log and email the main exception, it could be anything like database or networking.
   * @param e Single exception
   */
  private void processException(Exception e) {
    String engineerStr = "Check the following exception:<br />\n";
    engineerStr += Tool.getExceptionStr(e);
    engineerStr = String.format("<h2>PSI: System Error</h2><pre style='font-family: monospace;'>%s</pre>", engineerStr);
    Tool.pr(engineerStr);
    engineeringErrorNotification.send(engineerStr);
  }

  public String formatParentDealRecord(ParentDealRecord parent) {
    return String.format("<br />\n%s", parent.toString());
  }

  private void writeLastProcessedDateStr(String fname, String dateStr) {
    try {
      s3Util.writeStr(fname, dateStr);
    } catch (Exception e) {
      Tool.pr("Fail to write psi_last_processed.txt");
      Tool.pr(Tool.getExceptionStr(e));
    }
  }

  private String readLastProcessedDateStr(String fname) {
    String dateStr = null;
    dateStr = s3Util.readStr(fname);
    if (null != dateStr) {
      return dateStr;
    }
    String currentDateStr = Tool.getTimeStr(g.getTz());
    s3Util.writeStr(fname, currentDateStr);
    return currentDateStr;
  }

}
