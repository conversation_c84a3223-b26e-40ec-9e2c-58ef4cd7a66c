# islocal: true
local-api-url: "http://localhost:3001"
app:
  stagename: "Working Most Likely"
  sync-stage-names: "Working Most Likely,Lost,Order/Closed Won"
  deal-stagename: "RFP"
spring:
  datasource:
    url: ****************************************
    username: sms_user
    password: password
  jpa:
    # show-sql: true
    hibernate:
      format_sql: true
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl # Needed to execute queries with object names as defined in enities (i.e. QUARTER_YEAR instead of QuarterYear)
        default_schema: devpam
pam:
  api-url: "http://localhost:3001"
  service-user-sso: "206456089" # PAM Support user
s3:
  bucket: "adsales-appdev-config"
  bucket-path: "psi/last_updated/local"
  last-processed-date-format: "yyyy-MM-dd'T'HH:mm:ss"
  dump-folder: "tmp"
email:
  engineering-error:
    subject: "Salesforce Processing Error"
    recipients: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>
---
spring:
  config:
    activate:
      on-profile: "qa"
pam:
  api-url: "https://pamapiqa.nonprod.adsalescloud.inbcu.com"
s3:
  bucket-path: "psi/last_updated/qa"
---
spring:
  config:
    activate:
      on-profile: "prod"
  jpa:
    hibernate:
      naming:
        default_schema: pam
pam:
  api-url: "https://pamapiprod.adsalescloud.inbcu.com"
s3:
  bucket-path: "psi/last_updated/prod"
email:
  engineering-error:
    subject: "Salesforce Processing Error"
    recipients: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>
