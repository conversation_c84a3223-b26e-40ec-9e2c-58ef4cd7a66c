{"compositeResponse": [{"body": [{"errorCode": "PROCESSING_HALTED", "message": "The transaction was rolled back since another operation in the same transaction failed."}], "httpHeaders": {}, "httpStatusCode": 400, "referenceId": "reference_id_deal"}, {"body": [{"errorCode": "PROCESSING_HALTED", "message": "The transaction was rolled back since another operation in the same transaction failed."}], "httpHeaders": {}, "httpStatusCode": 400, "referenceId": "reference_id_deal_property"}, {"body": [{"errorCode": "PROCESSING_HALTED", "message": "The transaction was rolled back since another operation in the same transaction failed."}], "httpHeaders": {}, "httpStatusCode": 400, "referenceId": "refProduct2"}, {"body": [{"errorCode": "PROCESSING_HALTED", "message": "The transaction was rolled back since another operation in the same transaction failed."}], "httpHeaders": {}, "httpStatusCode": 400, "referenceId": "reference_id_opportunity"}, {"body": [{"errorCode": "PROCESSING_HALTED", "message": "The transaction was rolled back since another operation in the same transaction failed."}], "httpHeaders": {}, "httpStatusCode": 400, "referenceId": "refPricebookentry"}, {"body": [{"message": "field integrity exception: PricebookEntryId (pricebook entry is inactive)", "errorCode": "FIELD_INTEGRITY_EXCEPTION", "fields": ["PricebookEntryId"]}], "httpHeaders": {}, "httpStatusCode": 400, "referenceId": "reference_id_oppLineItem"}, {"body": [{"errorCode": "PROCESSING_HALTED", "message": "The transaction was rolled back since another operation in the same transaction failed."}], "httpHeaders": {}, "httpStatusCode": 400, "referenceId": "UpdateOpportunity"}]}