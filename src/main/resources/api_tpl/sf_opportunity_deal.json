{
  "url": "/services/data/v53.0/sobjects/Opportunity",
  "body": {
    "PAM_Parent_Deal_ID__c": "${parent_deal_id_with_year}",
    "PAM_Deal_ID__c": "${deal_id_with_year}",
    "RecordType": {
      "name": "${property_type:-}"
    },
    "Rating_Stream__c": "${rating_stream_name:-}",
    "isDigitalNews__c": ${is_digital:-},
    "OpportunityType__c": "Proactive",
    "Type": "Proactive",
    "Digital_Agency__c": "${sf_agency:-}",
    "AccountId": "${sf_advertiser:-}",
    "Upfront_Scatter__c": "${marketplace_name:-}",
    "Deal__c": "${sf_deal_id:-}",
    "Nielsen_Demo__c": "${demographic_name:-}",
    "Liability_Wipe__c": "No",
    "Name": "${deal_name:-}",
    "Calendar_Upfront_Year__c": "${fall_year:-}",
    "Season__c": "${season:-}",
    "Business__c": "${selling_vertical_name:-}",
    "AE_Cross_Sell_Partner__r": {
      "FederationIdentifier": "${client_ae_sso_id:-}"
    },
    "Owner": {
      "attributes": {
        "type": "User"
      },
      "FederationIdentifier": "${buying_ae_sso_id:-}"
    },
    "Account_Manager__r": {
      "FederationIdentifier": "${planner_sso_id:-}"
    },
    "Account_Manager1__r": {
      "FederationIdentifier": "${account_manager_sso_id:-}"
    },
    "StageName": "${stage:-}",
    "CloseDate": "${end_date:-}",
    "Measurement_Type__c": "${measurement_type}",
    "Currency_Type__c": "${currency}",
    "PAM_Q1__c": ${amount_q4},
    "PAM_Q2__c": ${amount_q1},
    "PAM_Q3__c": ${amount_q2},
    "PAM_Q4__c": ${amount_q3},
    "PAM_Pre_Quarter__c": ${amount_pre},
    "PAM_Post_Quarter__c": ${amount_post},
    "PAM_AccountId__c": "${sf_advertiser:-}",
    "PAM_Digital_Agency__c": "${sf_agency:-}",
    "PAM_Buying_AE__r": 
    { 
      "FederationIdentifier": "${buying_ae_sso_id:-}"
    }, 
    "PAM_AE_Cross_Sell_Partner__r":           
    { 
      "FederationIdentifier": "${client_ae_sso_id:-}"
    }, 
    "PAM_Nielsen_Demo__c": "${demographic_name:-}",
    "PAM_Upfront_Scatter__c": "${marketplace_name:-}",
    "PAM_Property__c": "${sf_property:-}",
    "PAM_Season__c": "${season:-}",
    "PAM_Calendar_Upfront_Year__c": "${fall_year:-}",
    "Deal_Year_ID__c": -1,
    "PAM_Total_Budget__c": ${amount}
  },  
  "method": "POST", 
  "referenceId": "${refid}" 
},
