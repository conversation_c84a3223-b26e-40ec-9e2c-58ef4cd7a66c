{
  "url": "/services/data/v53.0/sobjects/OpportunityLineItem",
  "body": {
    "PAM_Parent_Deal_ID__c": "${parent_deal_id_with_year}",
    "PAM_Deal_ID__c": "${deal_id_with_year}",
    "Quantity": 1,
    "Q1__c": ${amount_q4},
    "Q2__c": ${amount_q1},
    "Q3__c": ${amount_q2},
    "Q4__c": ${amount_q3},
    "Pre_Quarter__c": ${amount_pre},
    "Post_Quarter__c": ${amount_post},
    "UnitPrice": ${amount},
    "OpportunityId": "@{opportunity_deal(${deal_id}).id}",
    "PricebookEntryId": "@{pricebook_ref(${sf_property}).records[0].Id}",
    "ServiceDate": "${start_date:-}",
    "End_Date__c": "${end_date:-}",
    "Rating_Stream__c": "${rating_stream_name:-}",
    "Demo__c": "${demographic_name:-}",
    "Measurement_Type__c": "${measurement_type}",
    "Current_Year_Currency_Type__c": "${currency}"
  },
  "method": "POST",
  "referenceId": "${refid}" 
},
