def prCheck() {
  withCreds(this.&performPRChecks, null)
}

private def withCreds(method, args) {
  withCredentials([usernamePassword(credentialsId:'jenkins-adsales', usernameVariable:'GIT_USER', passwordVariable:'GIT_PASSWORD')]) {
      sh '''
          git config --global url.https://$GIT_PASSWORD:<EMAIL>/.insteadOf **************:
          git config --global --add url.https://$GIT_PASSWORD:<EMAIL>/.insteadOf https://github.com/
      '''
      
      args ? method(args) : method()
    }
}

private def performPRChecks() {
  sh './gradlew unitTest'
}
