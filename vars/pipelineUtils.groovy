def killOldRunningBuilds(pipelineName, currentBuildNumber) {
    def pipeline = Hudson.instance.getAllItems(org.jenkinsci.plugins.workflow.job.WorkflowJob).find { it.fullName.equals(pipelineName) }

    def currentBuildNum = currentBuildNumber as Integer
    def oldRunningBuilds = pipeline.getBuilds().findAll{ it.isBuilding() && it.getNumber() < currentBuildNum }

    oldRunningBuilds.each{build ->
        build.doStop()
    }
}

def boolean hasChangesIn(String path) {
  if (env.ghprbTargetBranch == null) {
    return true
  }

  def BASE = sh(
          returnStdout: true,
          script: "git rev-parse origin/${env.ghprbTargetBranch}"
  ).trim()

  def HEAD = sh(
          returnStdout: true,
          script: "git show -s --no-abbrev-commit --pretty=format:%P%n%H%n HEAD | tr ' ' '\n' | grep -v ${BASE} | head -n 1"
  ).trim()

  return sh(
          returnStatus: true,
          script: "git diff --name-only ${BASE}...${HEAD} | grep ^${path}"
  ) == 0
}
