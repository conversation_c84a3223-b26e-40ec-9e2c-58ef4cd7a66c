#!/bin/bash
DIR="$(cd "$(dirname "$0")" && pwd)"
image=$1
env=$2

space=sales-enablement-v1
envStack=$space-$2
appName=pam
appComponentName=psi
appCI=4382627

# Upload environment file to S3
s3Object=adsales-appdev-config/$appComponentName/$env.env
aws s3 cp $DIR/$env.env s3://$s3Object

# Deploy the stack
aws cloudformation deploy \
    --stack-name "$appComponentName-$env" \
    --parameter-overrides \
        "ApplicationName=$appName" \
        "ApplicationComponentName=$appComponentName" \
        "ServiceNowId=$appCI" \
        "TemplateURL=https://adsales-appdev-templates.s3.amazonaws.com/v1/sales-enablement-scheduled-v1-$env.yml" \
        "EnvironmentFile=arn:aws:s3:::$s3Object" \
        "Image=$image" \
    --template-file $DIR/stack.yml \
    --capabilities CAPABILITY_NAMED_IAM \
    --no-fail-on-empty-changeset
