script {
    library identifier: 'pam-psi@$env.GIT_COMMIT', retriever: legacySCM(scm)
}
pipeline {
  agent none
  stages {
    stage('Build and Publish qa') {
      agent {
        node {
          label 'JDK11'
        }
      }
      steps {
        script {
          sh './gp-publish-qa.sh qa'
        }
      }
    }
    stage("QA Deploy") {
      agent {
        node {
          label 'JDK11'
        }
      }
      steps {
        script {
          sh './deploy/deploy-qa.sh qa'
        }
      }
    }
    stage('Prod Deploy Approval') {
      steps {
        script {
          input message: "Deploy to prod?"
        }
      }
    }
    stage('Build and Publish prod') {
      agent {
        node {
          label 'JDK11'
        }
      }
      steps {
        script {
          sh './gp-publish-prod.sh prod'
        }
      }
    }
    stage('Prod Deploy') {
      agent {
        node {
          label 'JDK11'
        }
      }
      steps {
        script {
           sh './deploy/deploy-prod.sh prod'
        }
      }
    }
  }
}
