#!/bin/bash
repo=$1
account=$2
tags=$3

if test "$account" = '************'; then
    TEMP_ROLE=`aws sts assume-role --role-arn arn:aws:iam::************:role/jenkins-ecs --role-session-name dockerLogin`
    export AWS_ACCESS_KEY_ID=$(echo "${TEMP_ROLE}" | jq -r '.Credentials.AccessKeyId')
    export AWS_SECRET_ACCESS_KEY=$(echo "${TEMP_ROLE}" | jq -r '.Credentials.SecretAccessKey')
    export AWS_SESSION_TOKEN=$(echo "${TEMP_ROLE}" | jq -r '.Credentials.SessionToken')
fi

aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin $account.dkr.ecr.us-east-1.amazonaws.com

aws ecr describe-repositories --repository-names $repo || aws ecr create-repository --repository-name $repo

tags_array=(${tags//,/ })
for tag in "${tags_array[@]}"
do
    ./gradlew jib -PecrBuild -PecrAccount=$account -PecrTag=$tag
done
