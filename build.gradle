buildscript {
    ext {
        springBootVersion = '2.5'
    }
    repositories {
        maven { url 'https://plugins.gradle.org/m2/' }
        mavenCentral()
    }
    dependencies {
        classpath 'io.spring.gradle:dependency-management-plugin:1.0.11.RELEASE'
        classpath "org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}"
        classpath 'gradle.plugin.com.google.cloud.tools:jib-gradle-plugin:2.8.0'
    }
		dependencyLocking {
    	lockAllConfigurations()
	}
}

plugins {
	id 'org.springframework.boot' version '2.5.0'
	id 'io.spring.dependency-management' version '1.0.11.RELEASE'
	id 'java'
}

group = 'com.nbcu'
sourceCompatibility = '11'

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
	// all*.exclude module : 'spring-boot-starter-logging'
	// all*.exclude module : 'logback-classic'
}

repositories {
	mavenCentral()
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'com.oracle.database.jdbc:ojdbc8'
	implementation('org.springframework.boot:spring-boot-starter-jersey') {
		exclude module: "spring-boot-starter-tomcat"
	}
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	implementation 'com.google.code.gson:gson'
	implementation 'org.springframework.cloud:spring-cloud-starter-bootstrap:3.0.3'
	implementation 'org.springframework.cloud:spring-cloud-starter-aws-secrets-manager-config:2.2.6.RELEASE'
	implementation 'commons-io:commons-io:2.11.0'
	implementation 'org.apache.commons:commons-text:1.9'
	implementation platform('com.amazonaws:aws-java-sdk-bom:1.11.563')
	implementation 'com.amazonaws:aws-java-sdk-s3'
	implementation 'org.glassfish:javax.el:3.0.1-b08'
	implementation 'com.google.code.findbugs:jsr305:3.0.2'
	implementation 'com.amazonaws:aws-java-sdk-ses'
	implementation 'org.json:json:********'
	implementation 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'
	testImplementation('org.springframework.boot:spring-boot-starter-test') {
		exclude group: 'junit'
		exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
	}
}

apply plugin: 'com.google.cloud.tools.jib'

def baseImage = 'ghcr.io/nbcuniversal/dna-docker-images/dna-nbcu-jdk11:latest'
jib {
	def ecrBuild = project.hasProperty('ecrBuild')
	def ecrAccount = project.properties['ecrAccount'] ?: '************'
	def ecrTag = project.properties['ecrTag'] ?: 'latest'

	container.creationTime = Instant.now().toString()
	container.filesModificationTime = Instant.now().toString()
	allowInsecureRegistries = true
	from {
		image = baseImage
		auth {
				username "${System.env.GH_USER}"
				password "${System.env.GH_TOKEN}"
		}
	}
	to {
			to.image = "${ecrAccount}.dkr.ecr.us-east-1.amazonaws.com/pam-psi:${ecrTag}"
	}
}

// execute unit tests in a build script using "./gradlew unitTest"
def unitTest = tasks.register('unitTest', Test) {
	useJUnitPlatform {
		includeTags 'unit'
	}
}
